/**
 * 修复 http.api.js 中的 vm.$u 引用
 */

const fs = require('fs');

function fixHttpApi() {
  console.log('🔧 修复 http.api.js 中的 Vue 3 兼容性问题...');
  
  try {
    // 读取文件
    let content = fs.readFileSync('common/http.api.js', 'utf8');
    
    // 记录原始引用数量
    const originalMatches = (content.match(/vm\.\$u\./g) || []).length;
    console.log(`📊 发现 ${originalMatches} 个 vm.$u 引用需要修复`);
    
    // 替换所有 vm.$u 为 $u
    content = content.replace(/vm\.\$u\./g, '$u.');
    
    // 验证替换结果
    const remainingMatches = (content.match(/vm\.\$u\./g) || []).length;
    const fixedCount = originalMatches - remainingMatches;
    
    console.log(`✅ 修复了 ${fixedCount} 个引用`);
    console.log(`📊 剩余 ${remainingMatches} 个引用`);
    
    // 写回文件
    fs.writeFileSync('common/http.api.js', content, 'utf8');
    
    console.log('🎉 http.api.js 修复完成！');
    
    return remainingMatches === 0;
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    return false;
  }
}

// 运行修复
if (require.main === module) {
  const success = fixHttpApi();
  process.exit(success ? 0 : 1);
}

module.exports = { fixHttpApi };
