/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
import { createSSRApp } from 'vue';
import App from './App';

// 全局存储 vuex 的封装
import store from '@/store';

// 引入全局 uView 框架
import uView from 'uview-ui';

// Vue i18n 国际化
import { createI18n } from 'vue-i18n';

// i18n 部分的配置，引入语言包，注意路径
import lang_zh_CN from '@/common/locales/zh_CN.js';
import lang_en from '@/common/locales/en.js';

// http 拦截器
import httpInterceptor from '@/common/http.interceptor.js';

// http 接口 API 抽离
import httpApi from '@/common/http.api.js';

// 工具函数
import { replaceAll } from '@/common/common.js';

export function createApp() {
	const app = createSSRApp(App);

	// 使用 store
	app.use(store);

	// 使用 uView UI 框架 (临时兼容性处理)
	try {
		app.use(uView);
	} catch (error) {
		console.warn('uView UI 可能不完全兼容 Vue 3，请考虑升级到 uview-plus:', error);
		// 临时兼容性处理
		if (uView && uView.install) {
			uView.install(app);
		}
	}

	// 配置 i18n
	const i18n = createI18n({
		legacy: false, // 使用 Composition API 模式
		locale: 'zh_CN', // 默认语言
		fallbackLocale: 'en', // 回退语言
		messages: {
			'zh_CN': lang_zh_CN,
			'en': lang_en,
		}
	});
	app.use(i18n);

	// 全局属性
	app.config.globalProperties.replaceAll = replaceAll;

	// 使用 http 拦截器
	app.use(httpInterceptor);

	// 使用 http API
	app.use(httpApi);

	// 引入 uView 提供的对 vuex 的简写法文件
	try {
		let vuexStore = require('@/store/$u.mixin.js');
		if (vuexStore && vuexStore.default) {
			app.mixin(vuexStore.default);
		}
	} catch (error) {
		console.warn('vuex mixin not found:', error);
	}

	// 引入 uView 对小程序分享的 mixin 封装
	try {
		let mpShare = require('uview-ui/libs/mixin/mpShare.js');
		if (mpShare && mpShare.default) {
			app.mixin(mpShare.default);
		}
	} catch (error) {
		console.warn('mpShare mixin not found:', error);
	}

	return { app };
}
