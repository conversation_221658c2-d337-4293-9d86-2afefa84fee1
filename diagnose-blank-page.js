/**
 * 诊断页面空白问题
 */

const fs = require('fs');
const path = require('path');

function diagnoseBlankPage() {
  console.log('🔍 诊断页面空白问题...\n');
  
  const checks = [
    {
      name: 'App.vue 文件检查',
      test: () => {
        try {
          const content = fs.readFileSync('App.vue', 'utf8');
          return content.includes('<template>') && 
                 content.includes('</template>') &&
                 content.length > 100;
        } catch (e) {
          console.error('App.vue 读取失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'main.js 导出函数检查',
      test: () => {
        try {
          const content = fs.readFileSync('main.js', 'utf8');
          return content.includes('export function createApp') &&
                 content.includes('createSSRApp(App)') &&
                 content.includes('return { app }');
        } catch (e) {
          console.error('main.js 读取失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'pages.json 路由配置检查',
      test: () => {
        try {
          const content = fs.readFileSync('pages.json', 'utf8');
          const config = JSON.parse(content);
          return config.pages && 
                 config.pages.length > 0 &&
                 config.pages[0].path;
        } catch (e) {
          console.error('pages.json 解析失败:', e.message);
          return false;
        }
      }
    },
    {
      name: '首页文件存在检查',
      test: () => {
        try {
          const pagesConfig = JSON.parse(fs.readFileSync('pages.json', 'utf8'));
          const firstPage = pagesConfig.pages[0];
          if (!firstPage) return false;
          
          const pagePath = firstPage.path + '.vue';
          return fs.existsSync(pagePath);
        } catch (e) {
          console.error('首页文件检查失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'uview-plus 样式导入检查',
      test: () => {
        try {
          const content = fs.readFileSync('App.vue', 'utf8');
          return content.includes('uview-plus/index.scss');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'manifest.json Vue 3 配置检查',
      test: () => {
        try {
          const content = fs.readFileSync('manifest.json', 'utf8');
          const manifest = JSON.parse(content);
          return manifest.vueVersion === "3" || manifest.type === "vue3";
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'vite.config.js 配置检查',
      test: () => {
        try {
          const content = fs.readFileSync('vite.config.js', 'utf8');
          return content.includes('uni()') &&
                 content.includes('defineConfig');
        } catch (e) {
          return false;
        }
      }
    }
  ];
  
  let passedChecks = 0;
  const totalChecks = checks.length;
  const failedChecks = [];
  
  checks.forEach((check, index) => {
    process.stdout.write(`${index + 1}. ${check.name}... `);
    
    try {
      const result = check.test();
      if (result) {
        console.log('✅ 通过');
        passedChecks++;
      } else {
        console.log('❌ 失败');
        failedChecks.push(check.name);
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
      failedChecks.push(check.name);
    }
  });
  
  console.log(`\n📊 检查结果: ${passedChecks}/${totalChecks} 通过`);
  
  if (passedChecks === totalChecks) {
    console.log('🎉 基础配置检查通过！');
  } else {
    console.log('⚠️  发现问题，可能导致页面空白。');
  }
  
  // 输出详细的诊断建议
  console.log('\n🔧 页面空白常见原因和解决方案:');
  
  if (failedChecks.includes('App.vue 文件检查')) {
    console.log('\n❌ App.vue 问题:');
    console.log('• 检查 App.vue 是否存在且格式正确');
    console.log('• 确保包含 <template> 标签');
    console.log('• 检查是否有语法错误');
  }
  
  if (failedChecks.includes('main.js 导出函数检查')) {
    console.log('\n❌ main.js 问题:');
    console.log('• 确保使用 export function createApp()');
    console.log('• 检查是否正确返回 { app }');
    console.log('• 验证 createSSRApp 导入和使用');
  }
  
  if (failedChecks.includes('首页文件存在检查')) {
    console.log('\n❌ 首页文件问题:');
    console.log('• 检查 pages.json 中配置的首页文件是否存在');
    console.log('• 确保首页 .vue 文件路径正确');
  }
  
  console.log('\n🔍 其他可能的原因:');
  console.log('1. **JavaScript 错误**: 打开浏览器控制台查看错误信息');
  console.log('2. **CSS 问题**: 检查样式是否导致内容不可见');
  console.log('3. **路由问题**: 确保路由配置正确');
  console.log('4. **组件渲染问题**: 检查组件是否正确渲染');
  console.log('5. **依赖加载问题**: 确保所有依赖正确加载');
  
  console.log('\n📋 调试步骤:');
  console.log('1. 打开浏览器开发者工具 (F12)');
  console.log('2. 查看 Console 标签页的错误信息');
  console.log('3. 查看 Network 标签页的资源加载情况');
  console.log('4. 查看 Elements 标签页的 DOM 结构');
  console.log('5. 检查是否有 404 或其他网络错误');
  
  console.log('\n🚀 快速修复尝试:');
  console.log('• 清理缓存: rm -rf unpackage node_modules/.vite');
  console.log('• 重新安装: rm -rf node_modules && pnpm install');
  console.log('• 重启 HBuilderX 和项目');
  console.log('• 尝试不同的浏览器');
  
  return passedChecks === totalChecks;
}

// 运行诊断
if (require.main === module) {
  const success = diagnoseBlankPage();
  
  console.log('\n💡 如果问题仍然存在:');
  console.log('1. 请提供浏览器控制台的错误信息');
  console.log('2. 检查 HBuilderX 控制台的输出');
  console.log('3. 确认项目是否正确启动在 http://localhost:5100');
  
  process.exit(success ? 0 : 1);
}

module.exports = { diagnoseBlankPage };
