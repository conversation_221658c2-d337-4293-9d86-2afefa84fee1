/**
 * 验证编译错误修复脚本
 */

const fs = require('fs');
const path = require('path');

function verifyCompilationFixes() {
  console.log('🔍 开始验证编译错误修复...\n');
  
  const checks = [
    {
      name: 'pages.json JSON 格式验证',
      test: () => {
        try {
          const content = fs.readFileSync('pages.json', 'utf8');
          JSON.parse(content);
          return !content.includes('//') && !content.includes('/*');
        } catch (e) {
          console.error('pages.json 格式错误:', e.message);
          return false;
        }
      }
    },
    {
      name: 'pages.json 中无 uview-ui 路径引用',
      test: () => {
        try {
          const content = fs.readFileSync('pages.json', 'utf8');
          return !content.includes('uview-ui/components');
        } catch (e) {
          console.error('读取 pages.json 失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'tsconfig.json 路径配置正确',
      test: () => {
        try {
          const content = fs.readFileSync('tsconfig.json', 'utf8');
          const config = JSON.parse(content);
          return config.compilerOptions.paths['@/*'][0] === './*' &&
                 config.include.includes('**/*.vue') &&
                 !config.include.some(path => path.startsWith('src/'));
        } catch (e) {
          console.error('tsconfig.json 配置错误:', e.message);
          return false;
        }
      }
    },
    {
      name: 'vue.config.js 配置 uview-plus 转译',
      test: () => {
        try {
          const content = fs.readFileSync('vue.config.js', 'utf8');
          return content.includes('transpileDependencies') &&
                 content.includes('uview-plus');
        } catch (e) {
          console.error('vue.config.js 配置错误:', e.message);
          return false;
        }
      }
    },
    {
      name: 'main.js 使用 uview-plus',
      test: () => {
        try {
          const content = fs.readFileSync('main.js', 'utf8');
          return content.includes('uview-plus') && 
                 !content.includes('uview-ui');
        } catch (e) {
          console.error('main.js 配置错误:', e.message);
          return false;
        }
      }
    },
    {
      name: 'App.vue 样式导入正确',
      test: () => {
        try {
          const content = fs.readFileSync('App.vue', 'utf8');
          return content.includes('uview-plus/index.scss') &&
                 !content.includes('uview-ui/index.scss');
        } catch (e) {
          console.error('App.vue 样式配置错误:', e.message);
          return false;
        }
      }
    },
    {
      name: 'uview-plus 依赖已安装',
      test: () => {
        try {
          const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
          return packageJson.dependencies && packageJson.dependencies['uview-plus'];
        } catch (e) {
          console.error('package.json 检查失败:', e.message);
          return false;
        }
      }
    },
    {
      name: '旧 uview-ui 文件夹已清理',
      test: () => {
        return !fs.existsSync('uview-ui');
      }
    }
  ];
  
  let passedChecks = 0;
  const totalChecks = checks.length;
  
  checks.forEach((check, index) => {
    process.stdout.write(`${index + 1}. ${check.name}... `);
    
    try {
      const result = check.test();
      if (result) {
        console.log('✅ 通过');
        passedChecks++;
      } else {
        console.log('❌ 失败');
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
    }
  });
  
  console.log(`\n📊 验证结果: ${passedChecks}/${totalChecks} 通过`);
  
  if (passedChecks === totalChecks) {
    console.log('🎉 所有编译错误修复验证通过！');
    console.log('\n✨ 修复成果:');
    console.log('• ✅ JSON 格式问题已解决');
    console.log('• ✅ TypeScript 配置已修复');
    console.log('• ✅ uview-plus 迁移完成');
    console.log('• ✅ ES 模块兼容性已配置');
    console.log('• ✅ 路径引用问题已解决');
  } else {
    console.log('⚠️  部分检查失败，请查看上述错误信息。');
  }
  
  console.log('\n📋 下一步建议:');
  console.log('1. 在 HBuilderX 中重新编译项目');
  console.log('2. 检查编译控制台是否还有错误');
  console.log('3. 如果仍有错误，可能需要安装额外的 Babel 依赖');
  console.log('4. 测试应用启动和基本功能');
  
  if (passedChecks < totalChecks) {
    console.log('\n🔧 可能的解决方案:');
    console.log('• 检查 HBuilderX 版本是否支持 Vue 3 (需要 3.2.0+)');
    console.log('• 安装 Babel 依赖: pnpm add -D @babel/core @babel/preset-env');
    console.log('• 清理缓存: 删除 node_modules 和 unpackage 文件夹后重新安装');
  }
  
  return passedChecks === totalChecks;
}

// 运行验证
if (require.main === module) {
  const success = verifyCompilationFixes();
  process.exit(success ? 0 : 1);
}

module.exports = { verifyCompilationFixes };
