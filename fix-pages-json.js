/**
 * 修复 pages.json 中的 JSON 注释问题
 */

const fs = require('fs');

function fixPagesJson() {
  console.log('🔧 开始修复 pages.json 中的 JSON 注释问题...');
  
  try {
    // 读取 pages.json 文件
    let content = fs.readFileSync('pages.json', 'utf8');
    
    // 记录原始内容长度
    const originalLength = content.length;
    
    // 移除 JSON 注释
    // 1. 移除行尾注释 (// 注释)
    content = content.replace(/\s*\/\/.*$/gm, '');
    
    // 2. 移除多行注释 (/* 注释 */)
    content = content.replace(/\/\*[\s\S]*?\*\//g, '');
    
    // 3. 移除被注释掉的整个对象/数组项
    content = content.replace(/,\s*\/\/\s*\{[\s\S]*?\}\s*,?/g, ',');
    
    // 4. 清理多余的逗号和空行
    content = content.replace(/,(\s*[}\]])/g, '$1'); // 移除对象/数组末尾的多余逗号
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n'); // 合并多个空行
    
    // 5. 修复特定的注释行
    content = content.replace(/^\s*\/\/.*$/gm, ''); // 移除整行注释
    
    // 写回文件
    fs.writeFileSync('pages.json', content, 'utf8');
    
    const newLength = content.length;
    const savedBytes = originalLength - newLength;
    
    console.log('✅ pages.json 修复完成!');
    console.log(`📊 原始大小: ${originalLength} 字节`);
    console.log(`📊 修复后大小: ${newLength} 字节`);
    console.log(`📊 清理了: ${savedBytes} 字节的注释内容`);
    
    // 验证 JSON 格式
    try {
      JSON.parse(content);
      console.log('✅ JSON 格式验证通过!');
      return true;
    } catch (parseError) {
      console.error('❌ JSON 格式验证失败:', parseError.message);
      console.log('🔍 请手动检查 pages.json 文件');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 修复 pages.json 失败:', error.message);
    return false;
  }
}

// 运行修复
if (require.main === module) {
  const success = fixPagesJson();
  process.exit(success ? 0 : 1);
}

module.exports = { fixPagesJson };
