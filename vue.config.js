/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
module.exports = {
	configureWebpack: {
		devServer: {
			port : 8080,
			disableHostCheck : true,
			proxy : {
				"/pda" : {
					target : "http://127.0.0.1:8980",
					// target : "https://hcscmtest.zzfzyc.com/hcscm/pda/v1",
					changeOrigin : true,
					secure : false
				}
			}
		},
		module: {
			rules: [
				{
					test: /\.m?js$/,
					include: /node_modules\/uview-plus/,
					use: {
						loader: 'babel-loader',
						options: {
							presets: [
								['@babel/preset-env', {
									targets: {
										browsers: ['> 1%', 'last 2 versions', 'not ie <= 8']
									}
								}]
							],
							plugins: [
								'@babel/plugin-proposal-dynamic-import'
							]
						}
					}
				}
			]
		}
	},
	productionSourceMap: false,
	transpileDependencies: ['uview-plus']
}
