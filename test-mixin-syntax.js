/**
 * 测试 $u.mixin.js 语法修复
 */

const fs = require('fs');

function testMixinSyntax() {
  console.log('🔍 测试 $u.mixin.js 语法修复...\n');
  
  const tests = [
    {
      name: '$u.mixin.js 文件存在',
      test: () => {
        return fs.existsSync('store/$u.mixin.js');
      }
    },
    {
      name: '使用 export default 语法',
      test: () => {
        try {
          const content = fs.readFileSync('store/$u.mixin.js', 'utf8');
          return content.includes('export default {') && 
                 !content.includes('export {');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'beforeCreate 钩子语法正确',
      test: () => {
        try {
          const content = fs.readFileSync('store/$u.mixin.js', 'utf8');
          return content.includes('beforeCreate() {') &&
                 content.includes('this.$u.vuex = (name, value) => {');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'computed 属性语法正确',
      test: () => {
        try {
          const content = fs.readFileSync('store/$u.mixin.js', 'utf8');
          return content.includes('computed: {') &&
                 content.includes('...mapState($uStoreKey)');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'main.js 中正确导入 mixin',
      test: () => {
        try {
          const content = fs.readFileSync('main.js', 'utf8');
          return content.includes('vuexStore.default') &&
                 content.includes('app.mixin(vuexStore.default)');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'JavaScript 语法检查',
      test: () => {
        try {
          const content = fs.readFileSync('store/$u.mixin.js', 'utf8');
          // 基本语法检查
          const openBraces = (content.match(/\{/g) || []).length;
          const closeBraces = (content.match(/\}/g) || []).length;
          const openParens = (content.match(/\(/g) || []).length;
          const closeParens = (content.match(/\)/g) || []).length;
          
          return openBraces === closeBraces && openParens === closeParens;
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'Vuex 导入正确',
      test: () => {
        try {
          const content = fs.readFileSync('store/$u.mixin.js', 'utf8');
          return content.includes('import { mapState } from \'vuex\'') &&
                 content.includes('import store from "@/store"');
        } catch (e) {
          return false;
        }
      }
    }
  ];
  
  let passedTests = 0;
  const totalTests = tests.length;
  
  tests.forEach((test, index) => {
    process.stdout.write(`${index + 1}. ${test.name}... `);
    
    try {
      const result = test.test();
      if (result) {
        console.log('✅ 通过');
        passedTests++;
      } else {
        console.log('❌ 失败');
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
    }
  });
  
  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 $u.mixin.js 语法修复成功！');
    console.log('\n✨ 修复内容:');
    console.log('• ✅ 修复了 export 语法错误');
    console.log('• ✅ 使用正确的 export default');
    console.log('• ✅ beforeCreate 钩子语法正确');
    console.log('• ✅ computed 属性配置正确');
    console.log('• ✅ 与 Vue 3 兼容');
  } else {
    console.log('⚠️  部分测试失败，请检查语法。');
  }
  
  console.log('\n📋 现在可以尝试:');
  console.log('1. 在 HBuilderX 中重新运行项目');
  console.log('2. 检查是否还有其他语法错误');
  console.log('3. 验证 Vuex 状态管理功能');
  
  if (passedTests < totalTests) {
    console.log('\n🔧 如果仍有问题:');
    console.log('• 检查 store/index.js 是否正确配置');
    console.log('• 确保 Vuex 4 正确安装');
    console.log('• 检查其他 mixin 文件语法');
  }
  
  return passedTests === totalTests;
}

// 运行测试
if (require.main === module) {
  const success = testMixinSyntax();
  process.exit(success ? 0 : 1);
}

module.exports = { testMixinSyntax };
