
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/sys/login/index","pages/sys/login/forget","pages/sys/login/reg","pages/sys/msg/index","pages/sys/msg/form","pages/sys/home/<USER>","pages/sys/user/index","pages/sys/user/info","uview-ui/components/u-avatar-cropper/u-avatar-cropper","pages/sys/user/help","pages/sys/user/pwd","pages/sys/user/setting","pages/sys/user/comment","pages/sys/user/about","pages/testData/form","pages/testData/index","pages/common/webview","pages/sys/login/code","pages/sys/login/registerCode","pages/sys/user/service","pages/sys/user/problem","pages/sys/user/currency","pages/sys/user/clear-cache","pages/sys/workbench/index","pages/sys/book/index","pages/sys/book/personal-details","pages/sys/msg/list-item","pages/sys/workbench/add-form","pages/sys/msg/examine-item","pages/sys/msg/details","pages/sys/user/modify","pages/sys/workbench/install","pages/basedata/customer/customer","pages/basedata/chanpin/shop","pages/saleship/saleshiporder","pages/saleship/saleallocated","pages/saleship/saleallocatedadd","pages/storegoods/storegoods","pages/storegoods/dyeworksDyeback","pages/storegoods/dyeworksDyebackAdd","pages/storegoods/dyeworksDyebackDetail","pages/saleorder/saleorder","pages/storeyarn/storeYarnBusinessIn","pages/storeyarn/storeYarnBusinessInDetail","pages/storeyarn/storeYarnBusinessOut","pages/storeyarn/storeYarnBusinessOutAdd","pages/storeyarn/storeYarnBusinessOutDetail","pages/storeyarn/storeYarnBusinessCheck","pages/storeyarn/storeYarnBusinessCheckAdd","pages/storeyarn/storeYarnBusinessCheckDetail","pages/storeyarn/storeYarnInStore","pages/storeyarn/storeYarnProduceWeave","pages/storeyarn/storeYarnMoveStore","pages/storeyarn/storeyarnstoresearch","pages/storeyarn/storeyarnstoreallin","pages/storeyarn/storeyarnstoreallout","pages/storegoods/storegoodsstoresearch","pages/storegoods/storegoodsstoreallin","pages/storegoods/storegoodsstoreallout","pages/storegoods/storeGoodsBusinessIn","pages/storegoods/storeGoodsBusinessInAdd","pages/storegoods/storeGoodsBusinessInDetail","pages/storegoods/storeGoodsBusinessOut","pages/storegoods/storeGoodsBusinessOutAdd","pages/storegoods/storeGoodsBusinessOutDetail","pages/storegoods/storeGoodsBusinessCheck","pages/storegoods/storeGoodsBusinessCheckAdd","pages/storegoods/storeGoodsBusinessCheckDetail","pages/storegoods/storeGoodsBusinessCheckView","pages/storegoods/storeGoodsBusinessStationMoveOnly","pages/storegoods/storeGoodsBusinessStationMove","pages/storegoods/storeGoodsBusinessStationMoveAdd","pages/storegoods/storeGoodsBusinessStationMoveDetail","pages/storegoods/QRBarCodeReview","pages/storefabric/storefabricInBill","pages/storefabric/storefabricInBilladd","pages/storefabric/storefabricInStore","pages/storefabric/storefabricOutStore","pages/storefabric/storefabricstoresearch","pages/storefabric/storefabricstoreallin","pages/storefabric/storefabricstoreallout","pages/productorder/productweave","pages/productorder/productweaveview","pages/productorder/productweaveDetail","pages/saleship/salepickscan","pages/saleship/salepickscanview","pages/saleship/salepickscandetail","pages/saleship/saleShipmentOut","pages/saleship/saleShipmentOutView","pages/saleship/saleShipmentOutAdd","pages/saleship/saleShipmentOutDetail","pages/scan/scan","chanpin/view/chanpin","chanpin/view/addCp","chanpin/view/classify","chanpin/view/shop","chanpin/view/cpDetail","chanpin/view/searchAdmin","chanpin/view/search"],"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"Aidex","navigationBarBackgroundColor":"#ffffff"},"tabBar":{"color":"#333333","selectedColor":"#4094ff","backgroundColor":"#ffffff","borderStyle":"white","list":[{"pagePath":"pages/sys/msg/index","iconPath":"static/aidex/tabbar/msg_1.png","selectedIconPath":"static/aidex/tabbar/msg_2.png","text":"消息"},{"pagePath":"pages/sys/workbench/index","iconPath":"static/aidex/tabbar/apply_1.png","selectedIconPath":"static/aidex/tabbar/apply_2.png","text":"工作台"},{"pagePath":"pages/sys/book/index","iconPath":"static/aidex/tabbar/book_1.png","selectedIconPath":"static/aidex/tabbar/book_2.png","text":"通讯录"},{"pagePath":"pages/sys/user/index","iconPath":"static/aidex/tabbar/my_1.png","selectedIconPath":"static/aidex/tabbar/my_2.png","text":"我的"}]},"preloadRule":{},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"weex","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"appname":"浩拓技术1","compilerVersion":"4.66","subPackages":[{"root":"chanpin"}],"entryPagePath":"pages/sys/login/index","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/sys/login/index","meta":{"isQuit":true},"window":{"navigationBarTitleText":"登录"}},{"path":"/pages/sys/login/forget","meta":{},"window":{"navigationBarTitleText":"忘记密码"}},{"path":"/pages/sys/login/reg","meta":{},"window":{"navigationBarTitleText":"注册账号"}},{"path":"/pages/sys/msg/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"消息","navigationStyle":"custom"}},{"path":"/pages/sys/msg/form","meta":{},"window":{"navigationBarTitleText":"查看详情"}},{"path":"/pages/sys/home/<USER>","meta":{},"window":{"navigationBarTitleText":"工作台","navigationStyle":"custom"}},{"path":"/pages/sys/user/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"我的","navigationBarBackgroundColor":"#5b95ff","navigationBarTextStyle":"white","navigationStyle":"custom"}},{"path":"/pages/sys/user/info","meta":{},"window":{"navigationBarTitleText":"个人信息"}},{"path":"/uview-ui/components/u-avatar-cropper/u-avatar-cropper","meta":{},"window":{"navigationBarTitleText":"头像裁剪","navigationBarBackgroundColor":"#000000"}},{"path":"/pages/sys/user/help","meta":{},"window":{"navigationBarTitleText":"帮助中心"}},{"path":"/pages/sys/user/pwd","meta":{},"window":{"navigationBarTitleText":"修改密码"}},{"path":"/pages/sys/user/setting","meta":{},"window":{"navigationBarTitleText":"系统设置"}},{"path":"/pages/sys/user/comment","meta":{},"window":{"navigationBarTitleText":"意见反馈"}},{"path":"/pages/sys/user/about","meta":{},"window":{"navigationBarTitleText":"关于我们"}},{"path":"/pages/testData/form","meta":{},"window":{"navigationBarTitleText":"新增编辑"}},{"path":"/pages/testData/index","meta":{},"window":{"navigationBarTitleText":"增删改查"}},{"path":"/pages/common/webview","meta":{},"window":{"navigationBarTitleText":"浏览网页"}},{"path":"/pages/sys/login/code","meta":{},"window":{"navigationBarTitleText":"验证码"}},{"path":"/pages/sys/login/registerCode","meta":{},"window":{"navigationBarTitleText":"验证码"}},{"path":"/pages/sys/user/service","meta":{},"window":{"navigationBarTitleText":"联系客服"}},{"path":"/pages/sys/user/problem","meta":{},"window":{"navigationBarTitleText":"常见问题"}},{"path":"/pages/sys/user/currency","meta":{},"window":{"navigationBarTitleText":"通用"}},{"path":"/pages/sys/user/clear-cache","meta":{},"window":{"navigationBarTitleText":"清除缓存"}},{"path":"/pages/sys/workbench/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"工作台","navigationStyle":"custom"}},{"path":"/pages/sys/book/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"通讯录","navigationStyle":"custom"}},{"path":"/pages/sys/book/personal-details","meta":{},"window":{"navigationBarTitleText":"详情"}},{"path":"/pages/sys/msg/list-item","meta":{},"window":{"navigationBarTitleText":"列表"}},{"path":"/pages/sys/workbench/add-form","meta":{},"window":{"navigationBarTitleText":"请假申请"}},{"path":"/pages/sys/msg/examine-item","meta":{},"window":{"navigationBarTitleText":"网上报销"}},{"path":"/pages/sys/msg/details","meta":{},"window":{"navigationBarTitleText":"详情"}},{"path":"/pages/sys/user/modify","meta":{},"window":{"navigationBarTitleText":"修改"}},{"path":"/pages/sys/workbench/install","meta":{},"window":{"navigationBarTitleText":"常用设置","navigationStyle":"custom"}},{"path":"/pages/basedata/customer/customer","meta":{},"window":{"navigationBarTitleText":"客户资料","enablePullDownRefresh":false}},{"path":"/pages/basedata/chanpin/shop","meta":{},"window":{"navigationBarTitleText":"商品资料","enablePullDownRefresh":false}},{"path":"/pages/saleship/saleshiporder","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/saleship/saleallocated","meta":{},"window":{"navigationBarTitleText":"销售预约单","enablePullDownRefresh":false}},{"path":"/pages/saleship/saleallocatedadd","meta":{},"window":{"navigationBarTitleText":"新建销售预约单","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storegoods","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storegoods/dyeworksDyeback","meta":{},"window":{"navigationBarTitleText":"染整进仓","enablePullDownRefresh":false}},{"path":"/pages/storegoods/dyeworksDyebackAdd","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storegoods/dyeworksDyebackDetail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/saleorder/saleorder","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnBusinessIn","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnBusinessInDetail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnBusinessOut","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnBusinessOutAdd","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnBusinessOutDetail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnBusinessCheck","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnBusinessCheckAdd","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnBusinessCheckDetail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnInStore","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnProduceWeave","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeYarnMoveStore","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeyarnstoresearch","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeyarnstoreallin","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storeyarn/storeyarnstoreallout","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storegoodsstoresearch","meta":{},"window":{"navigationBarTitleText":"成品库存","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storegoodsstoreallin","meta":{},"window":{"navigationBarTitleText":"进仓查询","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storegoodsstoreallout","meta":{},"window":{"navigationBarTitleText":"出仓查询","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessIn","meta":{},"window":{"navigationBarTitleText":"成品进仓","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessInAdd","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessInDetail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessOut","meta":{},"window":{"navigationBarTitleText":"成品出仓","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessOutAdd","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessOutDetail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessCheck","meta":{},"window":{"navigationBarTitleText":"成品盘点","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessCheckAdd","meta":{},"window":{"navigationBarTitleText":"新增成品盘点","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessCheckDetail","meta":{},"window":{"navigationBarTitleText":"成品盘点明细","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessCheckView","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessStationMoveOnly","meta":{},"window":{"navigationBarTitleText":"成品移架明细","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessStationMove","meta":{},"window":{"navigationBarTitleText":"成品移架","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessStationMoveAdd","meta":{},"window":{"navigationBarTitleText":"新增成品移架","enablePullDownRefresh":false}},{"path":"/pages/storegoods/storeGoodsBusinessStationMoveDetail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storegoods/QRBarCodeReview","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storefabric/storefabricInBill","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storefabric/storefabricInBilladd","meta":{},"window":{"navigationBarTitleText":"新建胚布进仓单","enablePullDownRefresh":false}},{"path":"/pages/storefabric/storefabricInStore","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storefabric/storefabricOutStore","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storefabric/storefabricstoresearch","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storefabric/storefabricstoreallin","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/storefabric/storefabricstoreallout","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/productorder/productweave","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/productorder/productweaveview","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/productorder/productweaveDetail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/saleship/salepickscan","meta":{},"window":{"navigationBarTitleText":"成品配布","enablePullDownRefresh":false}},{"path":"/pages/saleship/salepickscanview","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/saleship/salepickscandetail","meta":{},"window":{"navigationBarTitleText":"成品配布明细","enablePullDownRefresh":false}},{"path":"/pages/saleship/saleShipmentOut","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/saleship/saleShipmentOutView","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/saleship/saleShipmentOutAdd","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/saleship/saleShipmentOutDetail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/scan/scan","meta":{},"window":{"navigationBarTitleText":"二维码","navigationStyle":"custom"}},{"path":"/chanpin/view/chanpin","meta":{},"window":{"navigationBarTitleText":"产品","navigationBarBackgroundColor":"#007AFF","navigationBarTextStyle":"white","enablePullDownRefresh":false}},{"path":"/chanpin/view/addCp","meta":{},"window":{"navigationBarTitleText":"新建产品","navigationBarBackgroundColor":"#007AFF","navigationBarTextStyle":"white","enablePullDownRefresh":false}},{"path":"/chanpin/view/classify","meta":{},"window":{"navigationBarTitleText":"产品分类","enablePullDownRefresh":false,"navigationBarBackgroundColor":"#007AFF","navigationBarTextStyle":"white"}},{"path":"/chanpin/view/shop","meta":{},"window":{"navigationBarTitleText":"商城","enablePullDownRefresh":false,"navigationBarBackgroundColor":"#007AFF","navigationBarTextStyle":"white"}},{"path":"/chanpin/view/cpDetail","meta":{},"window":{"navigationBarTitleText":"产品详情","enablePullDownRefresh":false,"navigationBarBackgroundColor":"#007AFF","navigationBarTextStyle":"white","navigationStyle":"custom"}},{"path":"/chanpin/view/searchAdmin","meta":{},"window":{"navigationBarTitleText":"搜索","enablePullDownRefresh":false,"navigationBarBackgroundColor":"#007AFF","navigationBarTextStyle":"white"}},{"path":"/chanpin/view/search","meta":{},"window":{"navigationBarTitleText":"搜索","enablePullDownRefresh":false,"navigationBarBackgroundColor":"#007AFF","navigationBarTextStyle":"white"}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
