/**
 * 批量替换 u-field 组件为 u-input
 */

const fs = require('fs');
const path = require('path');

function findVueFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 跳过 node_modules 和其他不需要的目录
      if (!['node_modules', '.git', 'unpackage', 'dist'].includes(file)) {
        findVueFiles(filePath, fileList);
      }
    } else if (file.endsWith('.vue')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

function replaceUFieldInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    // 检查是否包含 u-field
    if (content.includes('<u-field')) {
      console.log(`📝 处理文件: ${filePath}`);
      
      // 计算原始的 u-field 数量
      const originalMatches = (content.match(/<u-field/g) || []).length;
      console.log(`   发现 ${originalMatches} 个 u-field 组件`);
      
      // 替换 u-field 标签为 u-input
      content = content.replace(/<u-field/g, '<u-input');
      content = content.replace(/<\/u-field>/g, '</u-input>');
      
      // 替换一些常见的属性映射
      // label 属性在 u-input 中可能需要调整
      content = content.replace(/label="([^"]*)"([^>]*>)/g, (match, labelValue, rest) => {
        // 如果有 label，可以考虑使用 prefix 或者移除
        if (labelValue.includes('+86')) {
          return `prefix="${labelValue}"${rest}`;
        } else {
          return `placeholder="${labelValue}"${rest}`;
        }
      });
      
      // 替换 border-bottom 为 border
      content = content.replace(/:border-bottom="false"/g, ':border="false"');
      content = content.replace(/border-bottom="false"/g, 'border="false"');
      
      // 验证替换结果
      const remainingMatches = (content.match(/<u-field/g) || []).length;
      const replacedCount = originalMatches - remainingMatches;
      
      if (replacedCount > 0) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`   ✅ 替换了 ${replacedCount} 个组件`);
        hasChanges = true;
      }
      
      if (remainingMatches > 0) {
        console.log(`   ⚠️ 仍有 ${remainingMatches} 个 u-field 需要手动处理`);
      }
    }
    
    return hasChanges;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return false;
  }
}

function fixUFieldComponents() {
  console.log('🔧 开始批量替换 u-field 组件为 u-input...\n');
  
  try {
    // 查找所有 Vue 文件
    const vueFiles = findVueFiles('.');
    console.log(`📋 找到 ${vueFiles.length} 个 Vue 文件\n`);
    
    let totalProcessed = 0;
    let totalChanged = 0;
    
    // 处理每个文件
    vueFiles.forEach(filePath => {
      totalProcessed++;
      const hasChanges = replaceUFieldInFile(filePath);
      if (hasChanges) {
        totalChanged++;
      }
    });
    
    console.log(`\n📊 处理完成:`);
    console.log(`• 总文件数: ${totalProcessed}`);
    console.log(`• 修改文件数: ${totalChanged}`);
    console.log(`• 未修改文件数: ${totalProcessed - totalChanged}`);
    
    if (totalChanged > 0) {
      console.log('\n✅ u-field 组件替换完成！');
      console.log('\n📋 替换说明:');
      console.log('• <u-field> → <u-input>');
      console.log('• </u-field> → </u-input>');
      console.log('• label="+86" → prefix="+86"');
      console.log('• :border-bottom="false" → :border="false"');
      
      console.log('\n⚠️ 注意事项:');
      console.log('• 请检查替换后的组件是否正常工作');
      console.log('• 某些 label 属性可能需要手动调整');
      console.log('• 复杂的 u-field 用法可能需要手动修改');
    } else {
      console.log('\n✅ 没有找到需要替换的 u-field 组件');
    }
    
    return totalChanged > 0;
    
  } catch (error) {
    console.error('❌ 批量替换失败:', error.message);
    return false;
  }
}

// 运行修复
if (require.main === module) {
  const success = fixUFieldComponents();
  process.exit(success ? 0 : 1);
}

module.exports = { fixUFieldComponents };
