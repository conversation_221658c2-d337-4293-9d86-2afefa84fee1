<template>
	<view class="page-container">
		<!-- 条码扫描区域 -->
		<view class="scan-section">
			<view class="scan-header">
				<view class="scan-icon">
					<text class="barcode-icon">|||</text>
				</view>
				<text class="scan-text">扫码或手动输入条码</text>
				<view class="scan-options">
					<view class="radio-group">
						<view class="radio-item" @click="toggleScanMode('add')">
							<view class="radio-circle" :class="{ active: scanMode === 'add' }">
								<view class="radio-dot" v-if="scanMode === 'add'"></view>
							</view>
							<text class="radio-label">新增</text>
						</view>
						<view class="radio-item" @click="toggleScanMode('delete')">
							<view class="radio-circle" :class="{ active: scanMode === 'delete' }">
								<view class="radio-dot" v-if="scanMode === 'delete'"></view>
							</view>
							<text class="radio-label">删除</text>
						</view>
					</view>
				</view>
			</view>
			<view class="scan-input-container">
				<input
					class="scan-input"
					type="text"
					v-model="QRBarCode"
					:focus="testFocus1"
					@confirm="GoodsOutBillDetailScan"
					placeholder="请输入或扫描条码"
				/>
				<view class="confirm-btn" @click="GoodsOutBillDetailScan">确认</view>
			</view>
		</view>

		<!-- 基础信息区域 -->
		<view class="basic-info-section">
			<view class="section-header">
				<text class="section-title">基础信息</text>
			</view>

			<view class="form-container">
				<!-- 基本字段 -->
				<view class="form-item" @click="pickerSelectFun('单据类型')">
					<text class="form-label">类型</text>
					<view class="form-value">
						<text :class="BillTypeName ? 'value-text' : 'placeholder-text'">
							{{BillTypeName || '通过字典来配置'}}
						</text>
						<text class="arrow-icon">></text>
					</view>
				</view>

				<view class="form-item" @click="pickerSelectFun('营销部门')">
					<text class="form-label">营销体系</text>
					<view class="form-value">
						<text :class="PlanDepartmentName ? 'value-text' : 'placeholder-text'">
							{{PlanDepartmentName || '选择该用户能看到的体系'}}
						</text>
						<text class="arrow-icon">></text>
					</view>
				</view>

				<view class="form-item" @click="pickerSelectFun('仓库名称')">
					<text class="form-label">调出单位</text>
					<view class="form-value">
						<text :class="StoreName ? 'value-text' : 'placeholder-text'">
							{{StoreName || '调出单位'}}
						</text>
						<text class="arrow-icon">></text>
					</view>
				</view>

				<view class="form-item" @click="pickerSelectFun('调至仓库')">
					<text class="form-label">调入单位</text>
					<view class="form-value">
						<text :class="ToStoreName ? 'value-text' : 'placeholder-text'">
							{{ToStoreName || '接收单位'}}
						</text>
						<text class="arrow-icon">></text>
					</view>
				</view>

				<!-- 更多按钮 -->
				<view class="expand-btn" @click="toggleBasicInfo">
					<text class="expand-text" v-if="!showMoreBasicInfo">更多</text>
					<text class="expand-text" v-else>收起</text>
					<text class="expand-icon" :class="{ rotated: showMoreBasicInfo }">></text>
				</view>

				<!-- 展开的字段 -->
				<view v-if="showMoreBasicInfo" class="expanded-fields">
					<view class="form-item" @click="showDatePicker">
						<text class="form-label">日期</text>
						<view class="form-value">
							<text class="value-text">默认当日</text>
							<text class="arrow-icon">></text>
						</view>
					</view>

					<view class="form-item form-item-textarea">
						<text class="form-label">单据备注</text>
						<textarea
							class="form-textarea"
							v-model="Remark"
							placeholder="请输入"
							maxlength="200"
						></textarea>
					</view>

					<view class="form-item form-item-textarea">
						<text class="form-label">客厂用坯单号</text>
						<input
							class="form-input"
							v-model="customerOrderNo"
							placeholder="请输入"
						/>
					</view>
				</view>
			</view>
		</view>

		<!-- 出仓细码区域 -->
		<view class="detail-section">
			<view class="detail-header">
				<div class="detail-title">出仓细码</div>
				<div class="detail-summary-container">
					<text class="detail-summary">扫码添加还布</text>
					<text class="detail-count">{{fabricCount}}种还布，{{totalRolls}}匹，{{totalWeight}}kg</text>
				</div>
			</view>

			<view class="detail-list">
				<view v-for="(item, index) in OutDetailList" :key="index" class="detail-item">
					<view class="detail-item-header">
						<text class="detail-code">{{item.FabricName}}#{{item.FabricColorNo}}</text>
						<text class="detail-arrow">></text>
					</view>
					<view class="detail-item-content">
						<view class="detail-row">
							<text class="detail-label">纱批：{{item.FabricCrockNo || 'xxxx'}}</text>
							<text class="detail-label">机台：15#</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">等级：二等奖</text>
							<text class="detail-label">匹数：{{item.Roll || 3}}</text>
						</view>
					</view>
					<view class="detail-tag">
						<text class="tag-text">细码 ({{item.Qty || 0}}kg)</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="bottom-actions">
			<view class="action-btn save-btn" @click="submitBtnFun">
				<text class="btn-text">保存</text>
			</view>
			<view class="action-btn submit-btn" @click="commitBtnFun">
				<text class="btn-text">提交并审核</text>
			</view>
		</view>

		<!-- 选择器组件 -->
		<u-select v-model="selectShow" :list="selectList" @confirm="selectConfirmFun"></u-select>

		<!-- 消息提示 -->
		<u-toast ref="uToast"></u-toast>

		<!-- 消息显示 -->
		<view v-if="BillDataMessage" class="message-display">
			<text class="message-text">{{BillDataMessage}}</text>
		</view>
	</view>
</template>

<script>
	let that = '';
	import util, {
		parFabricGoodsBarCode2D,
		parYarnGoodsBarCode2D,
		playSuccessAudio,
		playErrorAudio
	} from '../../common/util';
	import wybTable from '@/components/wyb-table/wyb-table.vue';
	export default {
		data() {
			return {
				// 新增UI相关数据
				scanMode: 'add', // 扫描模式：add 或 delete
				showMoreBasicInfo: false, // 是否展开基础信息
				customerOrderNo: '', // 客厂用坯单号

				// 原有数据
				selectShow: false,
				selectList: [],
				selectType: '',
				pageType: '',
				commitType: '',
				actionSheetShow: false,
				testFocus0: true,
				testFocus1: false,
				QRBarCode: '',
				AllCrockNoScanStatus: false,
				BarCodeDelStatus: false,
				OutBillID: 0,
				OutBillNo: '',
				OutBillDate: '',
				BillBusinessID: '',
				BillTypeName: '',
				StoreNameID: '',
				StoreName: '',
				ToStoreNameID: '',
				ToStoreName: '',
				CustomerID: '',
				CustomerName: '',
				PlanDepartmentID: '',
				PlanDepartmentName: '',
				SaleUserID: '',
				SaleUserName: '',
				Remark: '',
				FabricName: '',
				FabricColorNo: '',
				FabricColorName: '',
				FabricCrockNo: '',
				testFocus0: true,
				FabricSumRoll: 0,
				FabricSumQty: 0,
				FabricCrockNoSumRoll: 0,
				FabricCrockNoSumQty: 0,
				OutDetailList: [],
				BillBusinessDataList: [],
				StoreNameDataList: [],
				ToStoreNameDataList: [],
				PlanDepartmentDataList: [],
				SaleUserDataList: [],
				BillDataMessage: '',
				CommitProcName: '', // 审核过程名称
				headersMaster: [{
					label: '坯布名称',
					key: 'FabricName'
				}, {
					label: '色号',
					key: 'FabricColorNo'
				}, {
					label: '颜色',
					key: 'FabricColorName'
				}, {
					label: '坯布缸号',
					key: 'FabricCrockNo'
				}, {
					label: '件数',
					key: 'Roll'
				}, {
					label: '长度',
					key: 'Qty'
				}],
			}
		},
		computed: {
			// 计算属性：面料种类数量
			fabricCount() {
				return this.OutDetailList.length;
			},
			// 计算属性：总匹数
			totalRolls() {
				return this.OutDetailList.reduce((sum, item) => sum + (item.Roll || 0), 0);
			},
			// 计算属性：总重量
			totalWeight() {
				return this.OutDetailList.reduce((sum, item) => sum + (parseFloat(item.Qty) || 0), 0);
			}
		},
		watch: {
			cpList: function(val, old) {
				that.cpCalcFun();
			}
		},
		onLoad(e) {
			that = this;
			this.getBillBusinessData();
			//this.getSaleUserData();
			//this.getPlanDepartmentData();

			setTimeout(() => {
				this.getStoreNameData();
			}, 500);
			setTimeout(() => {
				this.getPlanDepartmentData();
			}, 1000);

			if (getApp().globalData.StoreTypeNo.toUpperCase() == 'STOREFABRICGOODS') {
				this.StoreNameID = getApp().globalData.StoreNameID;
				this.StoreName = getApp().globalData.StoreName;
			};

			if (getApp().globalData.PlanDepartmentID != '') {
				this.PlanDepartmentID = getApp().globalData.PlanDepartmentID;
				this.PlanDepartmentName = getApp().globalData.PlanDepartmentName;
			}

			uni.$on('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$on('chanpinBindFun', that.chanpinBindFun)
			uni.$on('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$on('shangjiBindFun', that.shangjiBindFun)
		},

		onBackPress() {
			uni.$off('bjdKehuBindFun', that.bjdKehuBindFun)
			uni.$off('chanpinBindFun', that.chanpinBindFun)
			uni.$off('bjdLxrBindFun', that.bjdLxrBindFun)
			uni.$off('shangjiBindFun', that.shangjiBindFun)
		},
		methods: {
			// 新增UI交互方法
			toggleScanMode(mode) {
				this.scanMode = mode;
				// 同步更新原有的删除状态
				this.BarCodeDelStatus = (mode === 'delete');
			},

			toggleBasicInfo() {
				this.showMoreBasicInfo = !this.showMoreBasicInfo;
			},

			showDatePicker() {
				// 显示日期选择器的逻辑
				console.log('显示日期选择器');
			},

			playSuccess() {
				util.playSuccessAudio();
			},
			playError() {
				util.playErrorAudio();
			},

			AllCrockNoCheckChange: function() {
				this.AllCrockNoScanStatus = !this.AllCrockNoScanStatus;
			},

			BarCodeDelChange: function() {
				this.BarCodeDelStatus = !this.BarCodeDelStatus;
				// 同步更新扫描模式
				this.scanMode = this.BarCodeDelStatus ? 'delete' : 'add';
			},
			// 展示相应数据选择框
			pickerSelectFun: function(str) {
				that.selectList = [];
				if (str == '仓库名称') {
					console.log("---仓库名称->>>" + str);
					that.selectList = this.StoreNameDataList;
				} else if (str == '调至仓库') {
					that.selectList = this.ToStoreNameDataList;
				} else if (str == '单据类型') {
					that.selectList = this.BillBusinessDataList;
				} else if (str == '营销部门') {
					console.log("--营销部门-->>>" + str);
					that.selectList = this.PlanDepartmentDataList;
				} else if (str == '销售员') {
					that.selectList = this.SaleUserDataList;
				}
				console.log(JSON.stringify(that.selectList));
				that.selectShow = true;
				that.selectType = str;
			},
			// 选择框选中事件
			selectConfirmFun: function(e) {
				if (that.selectType == '仓库名称') {
					that.StoreNameID = e[0].value;
					that.StoreName = e[0].label;
				} else if (that.selectType == '调至仓库') {
					that.ToStoreNameID = e[0].value;
					that.ToStoreName = e[0].label;
				} else if (that.selectType == '单据类型') {
					that.BillBusinessID = e[0].value;
					that.BillTypeName = e[0].label;
				} else if (that.selectType == '营销部门') {
					that.PlanDepartmentID = e[0].value;
					that.PlanDepartmentName = e[0].label;
				} else if (that.selectType == '销售员') {
					that.SaleUserID = e[0].value;
					that.SaleUserName = e[0].label;
				}
			},

			getStoreNameData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreNameList',
							params: [{
								name: 'TypeNo',
								value: '%StoreFabricGoods%'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							console.log("--->>仓库名称<<<--" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreNameDataList.push({
									value: aResultData[i].StoreNameID,
									label: aResultData[i].StoreName
								});
								this.ToStoreNameDataList.push({
									value: aResultData[i].StoreNameID,
									label: aResultData[i].StoreName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.StoreNameDataList = [];
							this.ToStoreNameDataList = [];
						} else {
							this.StoreNameDataList = [];
							this.ToStoreNameDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			getPlanDepartmentData() {
				console.log("--->>>营销部门<<<---");
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetPlanDepartmentOwnerList',
							params: [{
								name: 'LoginID',
								value: getApp().globalData.LoginID
							}, {
								name: 'DefaultLoginID',
								value: getApp().globalData.LoginID
							}]
						},
					},
					success: (res) => {

						if (res.data.status == 0 && res.data.count > 0) {
							console.log("---->" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.PlanDepartmentDataList.push({
									value: aResultData[i].PlanDepartmentID,
									label: aResultData[i].PlanDepartmentName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.PlanDepartmentDataList = [];
						} else {
							this.PlanDepartmentDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			getSaleUserData() {
				console.log("--->>>销售员<<<---");
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.BaseData.GetSaleUserData',
							params: []
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							console.log("---->" + JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.SaleUserDataList.push({
									value: aResultData[i].SaleUserID,
									label: aResultData[i].SaleUserName
								})
							}
						} else {
							this.SaleUserDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			GetStoreStationName: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreStationName',
							params: [{
								name: 'No',
								value: this.StoreStationNo
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count > 0) {
							this.playSuccess();
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.StoreStationName = aResultData[i].StoreStationName;
							};
							this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
								this.testFocus0 = false;
							});
						} else {
							this.playError();
							this.StoreStationName = "",
								this.testFocus0 = false;
							this.$nextTick(() => {
								this.testFocus1 = false;
								this.testFocus0 = true;
							});
							this.StoreNameID = 0,
								this.BillDataMessage = '不存在当前的仓位资料，请确认！';
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			getBillBusinessData() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetBillBusinessList',
							params: [{
								name: 'TypeStatus',
								value: '2'
							}, {
								name: 'TypeNo',
								value: 'BusinessTypeStoreGoods'
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0 && res.data.count == 1) {
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.BillBusinessID = aResultData[i].BillBusinessID;
								this.BillTypeName = aResultData[i].BillTypeName;
								this.BillBusinessDataList.push({
									value: aResultData[i].BillBusinessID,
									label: aResultData[i].BillTypeName
								})
							}
						} else if (res.data.status == 0 && res.data.count > 0) {
							console.log("--->>单据类型<<--" +JSON.stringify(res.data));
							var aResultData = res.data.data;
							for (var i = 0; i < aResultData.length; i++) {
								this.BillBusinessDataList.push({
									value: aResultData[i].BillBusinessID,
									label: aResultData[i].BillTypeName
								})
							}
						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.BillBusinessDataList = [];
						} else {
							this.BillBusinessDataList = [];
						}
					},
					fail: (error) => {
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			GoodsOutBillDetailData: function() {
				uni.request({
					url: util.apiurl + 'rest/db/opensql',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							sql_command_id: 'APP.GetStoreGoodsBusinessOutDetailDataSQL',
							params: [{
								name: 'BillID',
								value: this.OutBillID
							}]
						},
					},
					success: (res) => {
						this.triggered = false;
						let data = res.data.data;
						console.log("---->" + JSON.stringify(res.data));
						if (res.data.status == 0 && res.data.count > 0) {
							var aResultData = res.data.data;
							this.OutDetailList = res.data.data;
							this.commitType = aResultData[0].CommitType;
							this.CommitProcName = aResultData[0].CommitProcName;
							this.FabricSumRoll = 0;
							this.FabricSumQty = 0;
							for (var i = 0; i < aResultData.length; i++) {
								if (parseFloat(aResultData[i].Roll) > 0) {
									this.FabricSumRoll = this.FabricSumRoll + aResultData[i].Roll;
								}

								if (parseFloat(aResultData[i].Qty) > 0) {
									this.FabricSumQty = this.FabricSumQty + aResultData[i].Qty;
								}
							};
							this.FabricSumRoll = this.FabricSumRoll.toFixed(2);
							this.FabricSumQty = this.FabricSumQty.toFixed(2);

						} else if (res.data.status == 0 && res.data.count <= 0) {
							this.OutDetailList = [];
						} else {
							this.OutDetailList = [];
						}

					},
				})
			},

			// 获取详细数据的点击事件处理
			GetDetailCrockNoListData: function(item) {
				console.log('点击详细数据:', item);
				// 这里可以添加点击详细数据的处理逻辑
			},

			// 日期修改
			bindDateChange: function(e) {
				that.bjdDate = e.detail.value;
				that.bjdDateTime = new Date(e.detail.value + ' 00:00:00').getTime()
			},
			// 展示相应数据选择框


			scanFun: function() {
				uni.scanCode({
					success(res) {
						that.code = res.result;
					}
				})
			},
			// 选择所属客户
			selectCustomer: function() {
				let aTypeName = "客户";
				if (this.BillTypeName == '成品销售出仓单') {
					aTypeName = "客户"
				}else if (this.BillTypeName == '成品退染出仓单') {
					aTypeName = "染整厂"
				}else if (this.BillTypeName == '销售调拨出仓单') {
					aTypeName = "客户"
				}else if (this.BillTypeName == '内部调拨出仓单') {
					aTypeName = "客户"
				}else if (this.BillTypeName == '成品采购退货单') {
					aTypeName = "成品供应商"
				}else if (this.BillTypeName == '成品其它出仓单') {
					aTypeName = ""
				};

				uni.navigateTo({
					url: '../basedata/customer/customer?type=' + aTypeName + ''
				})

			},
			// 绑定客户
			bjdKehuBindFun: function(e) {
				console.log("CustomerName===" + e.CustomerName);
				that.CustomerID = e.CustomerID;
				that.CustomerName = e.CustomerName;
				that.PlanDepartmentID = e.PlanDepartmentID;
				that.PlanDepartmentName = e.PlanDepartmentName;
				that.SaleUserID = e.SaleUserID;
				that.SaleUserName = e.SaleUserName;
				that.SaleCustomerAddress = e.CustomerAddress + ' ' + e.CustomerPhone + ' ' + e.CustomerLinkName;
			},

			// 提交按钮方法
			submitBtnFun: function() {
				if (this.PlanDepartmentName == '') {
					this.playError();
					this.BillDataMessage = '营销部门不能为空，请先输入营销部门！';
					return;
				}
				if (this.StoreName == '') {
					this.playError();
					this.BillDataMessage = '仓库名称不能为空，请先输入仓库名称！';
					return;
				}
				if (this.BillTypeName == '') {
					this.playError();
					this.BillDataMessage = '单据类型不能为空，请先输入单据类型！';
					return;
				}
				if (this.CustomerName == '') {
					this.playError();
					this.BillDataMessage = '往来单位不能为空，请先输入往来单位！';
					return;
				}

				if (this.BillTypeName == '销售调拨出仓单'){
					if (this.ToStoreName == '') {
						this.playError();
						this.BillDataMessage = '调至仓库不能为空，请先输入调至仓库！';
						return;
					}
				}

				if (this.BillTypeName == '内部调拨出仓单'){
					if (this.ToStoreName == '') {
						this.playError();
						this.BillDataMessage = '调至仓库不能为空，请先输入调至仓库！';
						return;
					}
				}

				if (this.OutBillID > 0) {
					this.playError();
					this.BillDataMessage = '当前单据已经提交，不能重复提交！';
					return;
				}

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreGoodsBusinessOutBillMaster',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.OutBillID
								},{
									name: '@BillBusinessID',
									value: this.BillBusinessID
								},{
									name: '@StoreNameID',
									value: this.StoreNameID
								},{
									name: '@ToStoreNameID',
									value: this.ToStoreNameID
								},{
									name: '@PlanDepartmentID',
									value: this.PlanDepartmentID
								},{
									name: '@CustomerID',
									value: this.CustomerID
								},{
									name: '@SaleUserID',
									value: this.SaleUserID
								},{
									name: '@Remark',
									value: this.Remark
								},{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.OutBillID = aResultData.GoodsOutBillID;
								this.OutBillNo = aResultData.GoodsOutBillNo;
								this.GoodsOutBillDetailData();
								this.BillDataMessage = "";
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '提交出错！' + aResultData.BillDataMessage;
								return;
							}
						} else {
							this.playError();
							this.BillDataMessage = '提交出错，' + res.data.msg
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},

			GoodsOutBillDetailScan() {
				if (this.StoreNameID == 0 && this.OutBillNo == '') {
					this.playError();
					this.BillDataMessage = '请先新增单据';
					return;
				}
				console.log("this.QRBarCode ---->>" + this.QRBarCode);

				let aBarCodeDelStatus = 0;
				if (this.BarCodeDelStatus){
					aBarCodeDelStatus = '1';
				};

				let aAllCrockNoScanStatus = 0;
				if (this.AllCrockNoScanStatus){
					aAllCrockNoScanStatus = '1';
				};

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: 'Usp_APP_StoreGoodsBusinessOutBillDetail',
							method: 'open_proc',
							params: [{
									name: '@BillMasterID',
									value: this.OutBillID
								},
								{
									name: '@QRBarCode',
									value: this.QRBarCode
								},
								{
									name: '@BarCodeDelStatus',
									value: aBarCodeDelStatus
								},
								{
									name: '@AllCrockNoScanStatus',
									value: aAllCrockNoScanStatus
								},
								{
									name: '@StoreStationName',
									value: this.StoreStationName
								},
								{
									name: '@UserName',
									value: getApp().globalData.UserName
								}
							]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							console.log(res.data.data.toString());
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage ==
								'SUCCESS') {
								this.playSuccess();
								this.FabricGoodsNo = aResultData.FabricGoodsNo;
								this.FabricGoodsName = aResultData.FabricGoodsName;
								this.GoodsCodeNo = aResultData.GoodsCodeNo;
								this.GoodsCodeName = aResultData.GoodsCodeName;
								this.CrockNo = aResultData.CrockNo;
								this.GoodsBillNo = aResultData.GoodsBillNo;
								this.GoodsQty = aResultData.GoodsQty;
								this.BillSumRoll = parseFloat(aResultData.BillSumRoll);
								this.BillSumQty = parseFloat(aResultData.BillSumQty);
								this.GoodsCodeSumRoll = parseFloat(aResultData.GoodsCodeSumRoll);
								this.GoodsCodeSumQty = parseFloat(aResultData.GoodsCodeSumQty);
								this.CrockNoSumRoll = parseFloat(aResultData.CrockNoSumRoll);
								this.CrockNoSumQty = parseFloat(aResultData.CrockNoSumQty);
								this.QRBarCode = '';
								this.BillDataMessage = '扫描成功！';
								this.GoodsOutBillDetailData();
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
							} else {
								this.playError();
								this.BillDataMessage = '出仓扫描出错，' + aResultData.BillDataMessage;
								this.QRBarCode = '';
								this.testFocus1 = false;
								this.$nextTick(() => {
									this.testFocus1 = true;
								});
								return;
							}

						} else {
							this.playError();
							this.QRBarCode = '';
							this.BillDataMessage = '出仓扫描出错，' + res.data.msg;
							this.testFocus1 = false;
							this.$nextTick(() => {
								this.testFocus1 = true;
							});
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.QRBarCode = '';
						this.testFocus1 = false;
						this.$nextTick(() => {
							this.testFocus1 = true;
							this.testFocus0 = false;
						});
						this.BillDataMessage = '出仓扫描出错，' + res.data.msg;
						uni.showToast({
							icon: 'none',
							title: '连接服务器出错，请检查后台服务是否启动！' + res.data.msg
						});
						setTimeout(function() {
							uni.hideLoading();
						}, 5000);
					},
				})
			},

			// 审核按钮方法
			commitBtnFun: function() {
				if (this.OutBillID <= 0) {
					this.playError();
					this.BillDataMessage = '当前单据未提交，不能审核或消审！';
					return;
				}

				var aCommitRecallName = '审核';
				if (this.commitType == ''){
					aCommitRecallName = '审核';
				} else {
					aCommitRecallName = '消审';
				}

				if (this.CommitProcName == ''){
					this.playError();
					this.BillDataMessage = '当前'+ aCommitRecallName +'操作配置有误，不能审核！';
					return;
				}

				uni.request({
					url: util.apiurl + 'rest/db/storedproc',
					data: {
						token: getApp().globalData.Token,
						format: 'json',
						data: {
							db_name: getApp().globalData.AppDBName,
							proc_name: this.CommitProcName,
							method: 'open_proc',
							params: [{
								name: '@BillID',
								value: this.OutBillID
							}, {
								name: '@BillNo',
								value: this.OutBillNo
							}, {
								name: '@UserName',
								value: getApp().globalData.UserName
							}]
						},
					},
					success: (res) => {
						if (res.data.status == 0) {
							var aResultData = JSON.parse(res.data.data);
							if (aResultData.BillDataStatus == '0' && aResultData.BillDataMessage == 'SUCCESS') {
								this.playSuccess();
								this.BillDataMessage = aCommitRecallName + "成功！";

								if (aCommitRecallName == '审核'){
									this.commitType = '已审核'
								} else {
									this.commitType = ''
								}
							} else {
								this.playError();
								this.BillDataMessage = aCommitRecallName + '出错！' + aResultData.BillDataMessage;
								return;
							}
						} else {
							this.playError();
							this.BillDataMessage = aCommitRecallName + '出错，' + res.data.msg
							return;
						}
					},
					fail: (error) => {
						this.playError();
						this.BillDataMessage = '连接服务器出错，请检查后台服务是否启动！' + res.data.msg;
					},
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
		padding-bottom: 160rpx;
	}

	.page-container {
		min-height: 100vh;
		background-color: #F5F5F5;
	}

	/* 条码扫描区域 */
	.scan-section {
		background-color: #FFFFFF;
		margin: 20rpx;
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.scan-header {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.scan-icon {
		margin-right: 16rpx;
	}

	.barcode-icon {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.scan-text {
		flex: 1;
		font-size: 32rpx;
		color: #333333;
	}

	.scan-options {
		display: flex;
		align-items: center;
	}

	.radio-group {
		display: flex;
		gap: 32rpx;
	}

	.radio-item {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.radio-circle {
		width: 32rpx;
		height: 32rpx;
		border: 2rpx solid #CCCCCC;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;

		&.active {
			border-color: #007AFF;
		}
	}

	.radio-dot {
		width: 16rpx;
		height: 16rpx;
		background-color: #007AFF;
		border-radius: 50%;
	}

	.radio-label {
		font-size: 28rpx;
		color: #333333;
	}

	.scan-input-container {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.scan-input {
		flex: 1;
		height: 80rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		padding: 0 24rpx;
		font-size: 28rpx;
		border: none;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #FFFFFF;
		padding: 20rpx 32rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		font-weight: 500;
	}

	/* 基础信息区域 */
	.basic-info-section {
		background-color: #FFFFFF;
		margin: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.section-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx;
		border-bottom: 1rpx solid #F0F0F0;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}

	.expand-btn {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.expand-text {
		font-size: 28rpx;
		color: #666666;
	}

	.expand-icon {
		font-size: 24rpx;
		color: #999999;
		transition: transform 0.3s ease;

		&.rotated {
			transform: rotate(90deg);
		}
	}

	.form-container {
		padding: 0 32rpx 32rpx;
	}

	.form-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx 0;
		border-bottom: 1rpx solid #F0F0F0;

		&:last-child {
			border-bottom: none;
		}

		&.form-item-textarea {
			flex-direction: column;
			align-items: flex-start;
			gap: 16rpx;
		}
	}

	.form-label {
		font-size: 28rpx;
		color: #333333;
		min-width: 160rpx;
	}

	.form-value {
		display: flex;
		align-items: center;
		gap: 16rpx;
		flex: 1;
		justify-content: flex-end;
	}

	.value-text {
		font-size: 28rpx;
		color: #333333;
	}

	.placeholder-text {
		font-size: 28rpx;
		color: #999999;
	}

	.arrow-icon {
		font-size: 24rpx;
		color: #CCCCCC;
	}

	.form-textarea {
		width: 100%;
		min-height: 120rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		padding: 16rpx;
		font-size: 28rpx;
		border: none;
		resize: none;
	}

	.form-input {
		width: 100%;
		height: 80rpx;
		background-color: #F8F8F8;
		border-radius: 8rpx;
		padding: 0 16rpx;
		font-size: 28rpx;
		border: none;
	}

	.expanded-fields {
		border-top: 1rpx solid #F0F0F0;
		margin-top: 16rpx;
		padding-top: 16rpx;
	}

	/* 出仓细码区域 */
	.detail-section {
		background-color: #FFFFFF;
		margin: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.detail-header {
		padding: 32rpx;
		border-bottom: 1rpx solid #F0F0F0;
	}

	.detail-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 8rpx;
	}
	.detail-summary-container {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.detail-summary {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 8rpx;
	}

	.detail-count {
		font-size: 24rpx;
		color: #666666;
	}

	.detail-list {
		padding: 0 32rpx 32rpx;
	}

	.detail-item {
		background-color: #F8F8F8;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 16rpx;
		position: relative;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.detail-item-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16rpx;
	}

	.detail-code {
		font-size: 28rpx;
		font-weight: 600;
		color: #333333;
	}

	.detail-arrow {
		font-size: 24rpx;
		color: #CCCCCC;
	}

	.detail-item-content {
		margin-bottom: 16rpx;
	}

	.detail-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 8rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.detail-label {
		font-size: 24rpx;
		color: #666666;
	}

	.detail-tag {
		position: absolute;
		bottom: 24rpx;
		right: 24rpx;
		background-color: #007AFF;
		border-radius: 20rpx;
		padding: 8rpx 16rpx;
	}

	.tag-text {
		font-size: 20rpx;
		color: #FFFFFF;
		font-weight: 500;
	}

	/* 底部按钮 */
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 24rpx 40rpx;
		border-top: 1rpx solid #F0F0F0;
		display: flex;
		gap: 24rpx;
		z-index: 100;
	}

	.action-btn {
		flex: 1;
		height: 88rpx;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.save-btn {
		background-color: #FFFFFF;
		border: 2rpx solid #007AFF;
	}

	.save-btn .btn-text {
		color: #007AFF;
		font-size: 32rpx;
		font-weight: 500;
	}

	.submit-btn {
		background-color: #007AFF;
	}

	.submit-btn .btn-text {
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: 500;
	}

	/* 消息显示 */
	.message-display {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba(0, 0, 0, 0.8);
		color: #FFFFFF;
		padding: 24rpx 32rpx;
		border-radius: 8rpx;
		z-index: 1000;
	}

	.message-text {
		font-size: 28rpx;
	}
</style>
