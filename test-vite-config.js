/**
 * 测试 vite.config.js 配置是否正确
 */

const fs = require('fs');

function testViteConfig() {
  console.log('🔍 测试 vite.config.js 配置...\n');
  
  const tests = [
    {
      name: 'vite.config.js 文件存在',
      test: () => {
        return fs.existsSync('vite.config.js');
      }
    },
    {
      name: '不包含 rollup-plugin-visualizer 导入',
      test: () => {
        try {
          const content = fs.readFileSync('vite.config.js', 'utf8');
          return !content.includes('rollup-plugin-visualizer');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: '包含 uni 插件',
      test: () => {
        try {
          const content = fs.readFileSync('vite.config.js', 'utf8');
          return content.includes('uni()');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: '包含 uview-plus 优化配置',
      test: () => {
        try {
          const content = fs.readFileSync('vite.config.js', 'utf8');
          return content.includes('optimizeDeps') && 
                 content.includes('uview-plus');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: '包含 import.meta.glob 兼容性处理',
      test: () => {
        try {
          const content = fs.readFileSync('vite.config.js', 'utf8');
          return content.includes('import.meta.glob');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: '包含代理配置',
      test: () => {
        try {
          const content = fs.readFileSync('vite.config.js', 'utf8');
          return content.includes('proxy') && 
                 content.includes('/pda');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'JavaScript 语法正确',
      test: () => {
        try {
          const content = fs.readFileSync('vite.config.js', 'utf8');
          // 基本语法检查
          return content.includes('export default defineConfig') &&
                 content.includes('plugins: [') &&
                 !content.includes('visualizer()') && // 确保已移除
                 content.split('{').length === content.split('}').length; // 括号匹配
        } catch (e) {
          return false;
        }
      }
    }
  ];
  
  let passedTests = 0;
  const totalTests = tests.length;
  
  tests.forEach((test, index) => {
    process.stdout.write(`${index + 1}. ${test.name}... `);
    
    try {
      const result = test.test();
      if (result) {
        console.log('✅ 通过');
        passedTests++;
      } else {
        console.log('❌ 失败');
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
    }
  });
  
  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 vite.config.js 配置正确！');
    console.log('\n✨ 配置特性:');
    console.log('• ✅ 移除了有问题的 visualizer 插件');
    console.log('• ✅ 保留了 uni 插件');
    console.log('• ✅ 配置了 uview-plus 优化');
    console.log('• ✅ 添加了 ES 模块兼容性处理');
    console.log('• ✅ 包含了 API 代理配置');
  } else {
    console.log('⚠️  部分测试失败，请检查配置。');
  }
  
  console.log('\n📋 现在可以尝试:');
  console.log('1. 在 HBuilderX 中重新运行项目');
  console.log('2. 选择 "运行 -> 运行到浏览器 -> Chrome"');
  console.log('3. 检查是否还有其他错误');
  
  if (passedTests < totalTests) {
    console.log('\n🔧 如果仍有问题:');
    console.log('• 检查 HBuilderX 控制台的具体错误信息');
    console.log('• 确保 HBuilderX 版本支持 Vue 3 + Vite');
    console.log('• 清理缓存: rm -rf unpackage node_modules/.vite');
  }
  
  return passedTests === totalTests;
}

// 运行测试
if (require.main === module) {
  const success = testViteConfig();
  process.exit(success ? 0 : 1);
}

module.exports = { testViteConfig };
