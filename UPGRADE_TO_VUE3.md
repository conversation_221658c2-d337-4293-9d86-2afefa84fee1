# Vue 2 到 Vue 3 升级指南

本文档记录了将项目从 Vue 2 升级到 Vue 3 的所有更改和注意事项。

## 🎯 升级概览

### 已完成的更改

#### 1. **核心依赖升级**
- ✅ Vue: `2.x` → `3.4.0`
- ✅ Vue-i18n: `8.20.0` → `9.8.0`
- ✅ Vuex: `3.x` → `4.1.0`
- ✅ @icon-park/vue: `1.3.5` → `@icon-park/vue-next: 1.4.2`

#### 2. **main.js 重构**
- ✅ 使用 `createSSRApp` 替代 `new Vue()`
- ✅ 使用 `createI18n` 替代 `new VueI18n()`
- ✅ 更新插件注册方式：`app.use()` 替代 `Vue.use()`
- ✅ 全局属性：`app.config.globalProperties` 替代 `Vue.prototype`

#### 3. **Store 升级**
- ✅ 使用 `createStore` 替代 `new Vuex.Store()`
- ✅ 移除 `Vue.use(Vuex)` 调用

#### 4. **组件升级**
- ✅ expandable-form 组件迁移到 Composition API
- ✅ 使用 `<script setup>` 语法
- ✅ 使用 `ref`, `computed`, `withDefaults`, `defineProps`

## 📋 详细更改记录

### main.js 更改

**之前 (Vue 2):**
```javascript
import Vue from 'vue'
import VueI18n from 'vue-i18n'
Vue.use(VueI18n)

const app = new Vue({
  i18n,
  store,
  ...App
})
```

**现在 (Vue 3):**
```javascript
import { createSSRApp } from 'vue'
import { createI18n } from 'vue-i18n'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(i18n)
  return { app }
}
```

### Store 更改

**之前 (Vue 2):**
```javascript
import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)

const store = new Vuex.Store({...})
```

**现在 (Vue 3):**
```javascript
import { createStore } from 'vuex'

const store = createStore({...})
```

### 组件更改

**之前 (Vue 2 Options API):**
```javascript
export default {
  props: {...},
  data() {
    return {...}
  },
  computed: {...},
  methods: {...}
}
```

**现在 (Vue 3 Composition API):**
```javascript
<script setup>
import { ref, computed } from 'vue'

const props = defineProps({...})
const data = ref(...)
const computedValue = computed(() => ...)
</script>
```

## ⚠️ 重要注意事项

### 1. **uView UI 兼容性问题** 🚨
**当前问题**: 项目使用的 uview-ui v1.8.3 **不支持 Vue 3**

**解决方案**:
```bash
# 选项1: 升级到 uview-plus (推荐)
npm uninstall uview-ui
npm install uview-plus

# 选项2: 使用 uview-ui v2.x (如果可用)
npm install uview-ui@^2.0.0
```

**需要修改的文件**:
- `main.js`: 更改导入路径
- `App.vue`: 更新样式导入
- 所有使用 uview 组件的页面

### 2. **uniapp 兼容性**
- 确保 uniapp 版本支持 Vue 3 (需要 HBuilderX 3.2.0+)
- 某些 uniapp 插件可能需要更新

### 3. **第三方库兼容性**
- wyb-table 组件需要检查 Vue 3 兼容性
- 其他自定义组件可能需要更新

### 4. **Vue 3 破坏性变更**
- `$children` 已移除
- `$listeners` 已移除
- 事件 API 变更
- 过滤器已移除
- `this.$refs` 行为变更

## 🔧 需要进一步处理的项目

### 1. **依赖安装**
```bash
npm install vue@^3.4.0 vue-i18n@^9.8.0 vuex@^4.1.0
npm install @icon-park/vue-next@^1.4.2
npm uninstall @icon-park/vue
```

### 2. **uView UI 升级**
检查并升级到支持 Vue 3 的版本：
```bash
# 可能需要升级到 uview-plus 或其他 Vue 3 兼容版本
npm install uview-plus
```

### 3. **其他组件检查**
- wyb-table 组件需要检查 Vue 3 兼容性
- 所有自定义组件需要逐一检查

### 4. **测试验证**
- [ ] 页面渲染正常
- [ ] 路由导航正常
- [ ] 状态管理正常
- [ ] 国际化功能正常
- [ ] 动画效果正常

## 🚀 下一步行动

1. **安装新依赖**：运行 `npm install` 安装更新的依赖
2. **测试基础功能**：确保应用能够正常启动
3. **逐页测试**：测试每个页面的功能
4. **修复兼容性问题**：处理发现的任何问题
5. **性能优化**：利用 Vue 3 的新特性优化性能

## 📚 参考资源

- [Vue 3 迁移指南](https://v3-migration.vuejs.org/)
- [Vuex 4 迁移指南](https://vuex.vuejs.org/guide/migrating-to-4-0-from-3-x.html)
- [Vue I18n 迁移指南](https://vue-i18n.intlify.dev/guide/migration/vue3.html)
- [uniapp Vue 3 支持](https://uniapp.dcloud.net.cn/tutorial/vue3-api.html)

## ✅ 升级检查清单

- [x] 更新 package.json 依赖
- [x] 重构 main.js
- [x] 升级 store
- [x] 更新 expandable-form 组件
- [ ] 测试应用启动
- [ ] 测试页面功能
- [ ] 检查第三方库兼容性
- [ ] 性能测试
- [ ] 生产环境部署测试
