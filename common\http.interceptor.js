/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作
const install = (app, vm) => {
	// 通用请求头设定
	const ajaxHeader = 'x-ajax';
	const sessionIdHeader = 'Authorization';
	const rememberMeHeader = 'x-remember';

	// Vue 3 中通过 app.config.globalProperties 访问全局属性
	const $u = app.config.globalProperties.$u;
	if (!$u || !$u.http) {
		console.warn('uView Plus $u.http 不可用，跳过 HTTP 拦截器配置');
		return;
	}

	// 为了兼容性，将 $u 方法也挂载到 app.config.globalProperties
	if (!app.config.globalProperties.$u.toast) {
		// 如果 toast 方法不存在，使用 uni.showToast 作为备用
		app.config.globalProperties.$u.toast = (title) => {
			uni.showToast({ title, icon: 'none' });
		};
	}

	if (!app.config.globalProperties.$u.vuex) {
		// 如果 vuex 方法不存在，提供一个空实现
		app.config.globalProperties.$u.vuex = (key, value) => {
			console.warn('$u.vuex 方法不可用:', key, value);
		};
	}

	$u.http.setConfig({
		baseUrl: vm.$store.state.apiurl,
		originalData: true, 
		// 默认头部，http2约定header名称统一小写 aidex
		header: {
      'Platform': 2,
			'content-type': 'application/x-www-form-urlencoded',
			'x-requested-with': 'XMLHttpRequest'
		}
	});
	
	// 请求拦截，配置Token等参数
	$u.http.interceptor.request = (req) => {
		if (!req.header){
			req.header = [];
		}
		req.header["source"] = "app";
		
		// 默认指定返回 JSON 数据
		if (!req.header[ajaxHeader]){
			req.header[ajaxHeader] = 'json';
		}
		
		// 设定传递 Token 认证参数 aidex
		if (!req.header[sessionIdHeader] && vm.vuex_token){
			req.header[sessionIdHeader] = vm.vuex_token;
		}
		
		// 为节省流量，记住我数据不是每次都发送的，当会话失效后，尝试重试登录 aidex
		if (!req.header[rememberMeHeader] && vm.vuex_remember && req.remember){
			req.header[rememberMeHeader] = vm.vuex_remember;
			req.remember = false;
		}
	    console.log('request', req);
		return req;
	}
	
	// 响应拦截，判断状态码是否通过
	$u.http.interceptor.response = async (res, req) => {
	    console.log('response', res);
		
		// 处理401未授权状态码
		if (res.statusCode === 401) {
			$u.toast('登录已过期，请重新登录');
			// 清除token和用户信息
			uni.removeStorageSync('token');
			$u.vuex('vuex_token', '');
			$u.vuex('vuex_user', {});
			// 跳转到登录页面
			uni.reLaunch({
				url: '/pages/sys/login/index'
			});
			return false;
		}
		if (!(res.data)){
			$u.toast('未连接到服务器')
			return Promise.reject(new Error('未连接到服务器'));
		}

		if(res.data.code !== 0) {
			$u.toast(res.data.msg)
			return Promise.reject(new Error(res.data.msg))
		}

		// 提取响应数据
		let responseData = res.data.data;
		console.log('HTTP 拦截器处理的数据:', responseData)

		// 处理用户认证相关数据
		if (typeof responseData === 'object' && !(responseData instanceof Array)){
			if (responseData.token){
				console.log('更新 token:', responseData.token)
				$u.vuex('vuex_token', responseData.token);
				if (responseData.user){
					$u.vuex('vuex_user', responseData.user);
				}
				$u.vuex('vuex_isAgent', responseData.isAgent);
			}
			if (responseData.result === 'login'){
				$u.vuex('vuex_user', {});
				if (req.tryagain == undefined || req.tryagain){
					req.tryagain = false; req.remember = true;
					await $u.http.request(req).then(res => {
						responseData = res;
					});
				}
				if (responseData.result === 'login'){
					if (!req.data.loginCheck){
						$u.toast(responseData.msg);
					}
					req.tryagain = true;
				}
			}
		}

		// 处理记住我功能
		if (res.header && res.header[rememberMeHeader]){
			let remember = res.header[rememberMeHeader];
			if (remember && remember != 'deleteMe'){
				vm.$u.vuex('vuex_remember', remember);
			}else{
				vm.$u.vuex('vuex_remember', '');
			}
		}

		// 返回处理后的数据，保持原有的数据结构以确保兼容性
		// 对于列表数据，直接返回数组；对于其他数据，返回完整的响应结构
		if (Array.isArray(responseData)) {
			return responseData;
		} else if (responseData && typeof responseData === 'object') {
			// 如果响应数据有 list 属性，说明是分页数据，返回完整结构
			if (responseData.list !== undefined) {
				return responseData;
			}
			// 其他情况返回原数据
			return responseData;
		}

		return responseData;
	}
	
	// 封装 get text 请求
	vm.$u.getText = (url, data = {}, header = {}) => {
		return vm.$u.http.request({
			dataType: 'text',
			method: 'GET',
			url,
			header,
			data
		})
	}
	
	// 封装 post json 请求
	vm.$u.postJson = (url, data = {}, header = {}) => {
		console.log('header', header, url)
		header['content-type'] = 'application/json';
		return vm.$u.http.request({
			url,
			method: 'POST',
			header,
			data
		})
	}
	
}

export default {
	install
}
