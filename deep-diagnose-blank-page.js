/**
 * 深度诊断页面空白问题
 */

const fs = require('fs');
const path = require('path');

function deepDiagnoseBlankPage() {
  console.log('🔍 深度诊断页面空白问题...\n');
  
  const issues = [];
  
  // 检查首页组件的详细内容
  console.log('📋 检查首页组件详情:');
  try {
    const pagesConfig = JSON.parse(fs.readFileSync('pages.json', 'utf8'));
    const firstPage = pagesConfig.pages[0];
    const pageFile = firstPage.path + '.vue';
    
    console.log(`• 首页路径: ${firstPage.path}`);
    console.log(`• 首页文件: ${pageFile}`);
    
    if (fs.existsSync(pageFile)) {
      const pageContent = fs.readFileSync(pageFile, 'utf8');
      
      // 检查组件结构
      const hasTemplate = pageContent.includes('<template>');
      const hasScript = pageContent.includes('<script>');
      const hasExportDefault = pageContent.includes('export default');
      const hasOnLoad = pageContent.includes('onLoad');
      const hasOnShow = pageContent.includes('onShow');
      
      console.log(`• Template 标签: ${hasTemplate ? '✅' : '❌'}`);
      console.log(`• Script 标签: ${hasScript ? '✅' : '❌'}`);
      console.log(`• Export default: ${hasExportDefault ? '✅' : '❌'}`);
      console.log(`• onLoad 钩子: ${hasOnLoad ? '✅' : '❌'}`);
      console.log(`• onShow 钩子: ${hasOnShow ? '✅' : '❌'}`);
      
      // 检查是否有语法错误
      const templateMatch = pageContent.match(/<template>([\s\S]*?)<\/template>/);
      if (templateMatch) {
        const templateContent = templateMatch[1].trim();
        if (templateContent.length < 10) {
          console.log('⚠️  Template 内容过少，可能为空');
          issues.push('首页 template 内容过少');
        }
      }
      
      // 检查是否有明显的语法错误
      if (pageContent.includes('export {')) {
        console.log('❌ 发现错误的 export 语法');
        issues.push('首页组件使用了错误的 export 语法');
      }
      
    } else {
      console.log('❌ 首页文件不存在');
      issues.push('首页文件不存在');
    }
  } catch (e) {
    console.log('❌ 无法检查首页组件:', e.message);
    issues.push('无法读取首页组件');
  }
  
  // 检查 main.js 详情
  console.log('\n📋 检查 main.js 详情:');
  try {
    const mainContent = fs.readFileSync('main.js', 'utf8');
    
    const hasCreateSSRApp = mainContent.includes('createSSRApp');
    const hasExportCreateApp = mainContent.includes('export function createApp');
    const hasReturnApp = mainContent.includes('return { app }');
    const hasUviewPlus = mainContent.includes('uview-plus');
    
    console.log(`• createSSRApp: ${hasCreateSSRApp ? '✅' : '❌'}`);
    console.log(`• export function createApp: ${hasExportCreateApp ? '✅' : '❌'}`);
    console.log(`• return { app }: ${hasReturnApp ? '✅' : '❌'}`);
    console.log(`• uview-plus 导入: ${hasUviewPlus ? '✅' : '❌'}`);
    
    // 检查是否有语法错误
    if (mainContent.includes('app.mount(')) {
      console.log('⚠️  发现 app.mount() 调用，uniapp 不需要手动挂载');
      issues.push('main.js 中有不必要的 app.mount() 调用');
    }
    
  } catch (e) {
    console.log('❌ 无法检查 main.js:', e.message);
    issues.push('无法读取 main.js');
  }
  
  // 检查 App.vue 详情
  console.log('\n📋 检查 App.vue 详情:');
  try {
    const appContent = fs.readFileSync('App.vue', 'utf8');
    
    const hasTemplate = appContent.includes('<template>');
    const hasRouterView = appContent.includes('<router-view');
    const hasOnLaunch = appContent.includes('onLaunch');
    const hasGlobalData = appContent.includes('globalData');
    
    console.log(`• Template 标签: ${hasTemplate ? '✅' : '❌'}`);
    console.log(`• router-view (应该没有): ${hasRouterView ? '❌' : '✅'}`);
    console.log(`• onLaunch 钩子: ${hasOnLaunch ? '✅' : '❌'}`);
    console.log(`• globalData: ${hasGlobalData ? '✅' : '❌'}`);
    
    if (hasRouterView) {
      issues.push('App.vue 中仍有 router-view');
    }
    
  } catch (e) {
    console.log('❌ 无法检查 App.vue:', e.message);
    issues.push('无法读取 App.vue');
  }
  
  // 检查依赖
  console.log('\n📋 检查关键依赖:');
  try {
    const packageContent = fs.readFileSync('package.json', 'utf8');
    const pkg = JSON.parse(packageContent);
    
    const vueVersion = pkg.dependencies?.vue;
    const uviewPlusVersion = pkg.dependencies['uview-plus'];
    const vuexVersion = pkg.dependencies?.vuex;
    
    console.log(`• Vue 版本: ${vueVersion || '未安装'}`);
    console.log(`• uview-plus 版本: ${uviewPlusVersion || '未安装'}`);
    console.log(`• Vuex 版本: ${vuexVersion || '未安装'}`);
    
    if (!vueVersion || !vueVersion.startsWith('^3')) {
      issues.push('Vue 版本不正确');
    }
    if (!uviewPlusVersion) {
      issues.push('uview-plus 未安装');
    }
    
  } catch (e) {
    console.log('❌ 无法检查依赖:', e.message);
    issues.push('无法读取 package.json');
  }
  
  // 检查编译产物
  console.log('\n📋 检查编译产物:');
  const unpackageExists = fs.existsSync('unpackage');
  const distExists = fs.existsSync('unpackage/dist');
  
  console.log(`• unpackage 目录: ${unpackageExists ? '✅' : '❌'}`);
  console.log(`• dist 目录: ${distExists ? '✅' : '❌'}`);
  
  if (distExists) {
    try {
      const distFiles = fs.readdirSync('unpackage/dist');
      console.log(`• 编译文件数量: ${distFiles.length}`);
      
      const hasH5 = distFiles.includes('build') || distFiles.includes('h5');
      console.log(`• H5 编译产物: ${hasH5 ? '✅' : '❌'}`);
      
      if (!hasH5) {
        issues.push('缺少 H5 编译产物');
      }
    } catch (e) {
      console.log('❌ 无法检查编译产物详情');
    }
  }
  
  // 总结问题
  console.log('\n📊 诊断总结:');
  if (issues.length === 0) {
    console.log('🎉 未发现明显的配置问题');
  } else {
    console.log(`⚠️  发现 ${issues.length} 个潜在问题:`);
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }
  
  // 提供具体的解决建议
  console.log('\n🔧 具体解决建议:');
  
  if (issues.includes('首页 template 内容过少')) {
    console.log('\n❌ 首页 Template 问题:');
    console.log('• 检查首页组件的 <template> 是否有实际内容');
    console.log('• 确保至少有一个可见的元素');
    console.log('• 建议添加简单的测试内容验证渲染');
  }
  
  if (issues.includes('首页组件使用了错误的 export 语法')) {
    console.log('\n❌ Export 语法问题:');
    console.log('• 确保使用 export default { ... }');
    console.log('• 不要使用 export { ... }');
  }
  
  if (issues.includes('App.vue 中仍有 router-view')) {
    console.log('\n❌ App.vue 路由问题:');
    console.log('• 移除 <router-view /> 标签');
    console.log('• uniapp 不使用 Vue Router');
  }
  
  console.log('\n💡 通用解决步骤:');
  console.log('1. 创建最简单的测试页面验证渲染');
  console.log('2. 检查 HBuilderX 控制台的详细错误信息');
  console.log('3. 在首页组件中添加更多调试信息');
  console.log('4. 尝试使用不同的浏览器测试');
  
  return issues.length === 0;
}

// 运行深度诊断
if (require.main === module) {
  const success = deepDiagnoseBlankPage();
  process.exit(success ? 0 : 1);
}

module.exports = { deepDiagnoseBlankPage };
