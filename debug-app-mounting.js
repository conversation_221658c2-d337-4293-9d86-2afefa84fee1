/**
 * 调试应用挂载问题
 */

const fs = require('fs');

function debugAppMounting() {
  console.log('🔍 调试应用挂载问题...\n');
  
  const checks = [
    {
      name: 'App.vue template 检查',
      test: () => {
        try {
          const content = fs.readFileSync('App.vue', 'utf8');
          // uniapp 的 App.vue 可以有空的 template 或者简单的注释
          return content.includes('<template>') && 
                 !content.includes('<router-view />'); // 不应该有 router-view
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'pages.json 首页配置检查',
      test: () => {
        try {
          const content = fs.readFileSync('pages.json', 'utf8');
          const config = JSON.parse(content);
          const firstPage = config.pages[0];
          return firstPage && firstPage.path;
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: '首页文件存在且有效',
      test: () => {
        try {
          const pagesConfig = JSON.parse(fs.readFileSync('pages.json', 'utf8'));
          const firstPage = pagesConfig.pages[0];
          if (!firstPage) return false;
          
          const pagePath = firstPage.path + '.vue';
          if (!fs.existsSync(pagePath)) return false;
          
          const pageContent = fs.readFileSync(pagePath, 'utf8');
          return pageContent.includes('<template>') && 
                 pageContent.includes('</template>') &&
                 pageContent.length > 100;
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'main.js Vue 3 语法检查',
      test: () => {
        try {
          const content = fs.readFileSync('main.js', 'utf8');
          return content.includes('createSSRApp') &&
                 content.includes('export function createApp') &&
                 content.includes('return { app }');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'uview-plus easycom 配置检查',
      test: () => {
        try {
          const content = fs.readFileSync('pages.json', 'utf8');
          const config = JSON.parse(content);
          return config.easycom && 
                 config.easycom.custom &&
                 config.easycom.custom['^u--(.*)'];
        } catch (e) {
          return false;
        }
      }
    }
  ];
  
  let passedChecks = 0;
  const totalChecks = checks.length;
  const issues = [];
  
  checks.forEach((check, index) => {
    process.stdout.write(`${index + 1}. ${check.name}... `);
    
    try {
      const result = check.test();
      if (result) {
        console.log('✅ 通过');
        passedChecks++;
      } else {
        console.log('❌ 失败');
        issues.push(check.name);
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
      issues.push(check.name);
    }
  });
  
  console.log(`\n📊 检查结果: ${passedChecks}/${totalChecks} 通过`);
  
  // 输出首页信息
  try {
    const pagesConfig = JSON.parse(fs.readFileSync('pages.json', 'utf8'));
    const firstPage = pagesConfig.pages[0];
    console.log(`\n📋 首页配置信息:`);
    console.log(`• 首页路径: ${firstPage.path}`);
    console.log(`• 首页标题: ${firstPage.style?.navigationBarTitleText || '未设置'}`);
    console.log(`• 首页文件: ${firstPage.path}.vue`);
  } catch (e) {
    console.log('\n❌ 无法读取首页配置');
  }
  
  if (passedChecks === totalChecks) {
    console.log('\n🎉 应用挂载配置检查通过！');
  } else {
    console.log('\n⚠️  发现配置问题，可能影响应用挂载。');
  }
  
  console.log('\n🔧 uniapp 应用挂载机制:');
  console.log('• uniapp 不使用传统的 Vue Router');
  console.log('• 页面路由由 pages.json 管理');
  console.log('• App.vue 主要用于全局样式和逻辑');
  console.log('• 首页由 pages.json 的第一个页面决定');
  
  console.log('\n📋 调试建议:');
  console.log('1. 检查浏览器 Network 标签，确认 main.js 正确加载');
  console.log('2. 在首页组件中添加 console.log 确认组件是否执行');
  console.log('3. 检查 HBuilderX 控制台是否有编译错误');
  console.log('4. 尝试在首页 mounted 钩子中添加调试信息');
  
  if (issues.length > 0) {
    console.log('\n🔧 需要修复的问题:');
    issues.forEach(issue => {
      console.log(`• ${issue}`);
    });
  }
  
  console.log('\n💡 添加调试信息建议:');
  console.log('在首页组件 (pages/sys/login/index.vue) 中添加:');
  console.log(`
mounted() {
  console.log('首页组件已挂载');
  console.log('当前页面:', this.$route);
},
created() {
  console.log('首页组件已创建');
}
  `);
  
  return passedChecks === totalChecks;
}

// 运行调试
if (require.main === module) {
  const success = debugAppMounting();
  process.exit(success ? 0 : 1);
}

module.exports = { debugAppMounting };
