/**
 * 测试 H5 配置修复
 */

const fs = require('fs');

function testH5ConfigFix() {
  console.log('🔍 测试 H5 配置修复...\n');
  
  const checks = [
    {
      name: 'manifest.json H5 template 配置',
      test: () => {
        try {
          const content = fs.readFileSync('manifest.json', 'utf8');
          const manifest = JSON.parse(content);
          return manifest.h5.template === 'index.html';
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'manifest.json H5 base 路径配置',
      test: () => {
        try {
          const content = fs.readFileSync('manifest.json', 'utf8');
          const manifest = JSON.parse(content);
          return manifest.h5.router.base === '/';
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'index.html 模板文件存在',
      test: () => {
        return fs.existsSync('index.html');
      }
    },
    {
      name: 'index.html 包含正确的应用容器',
      test: () => {
        try {
          const content = fs.readFileSync('index.html', 'utf8');
          return content.includes('<div id="app">') &&
                 content.includes('<!--app-html-->');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'index.html 包含 main.js 引用',
      test: () => {
        try {
          const content = fs.readFileSync('index.html', 'utf8');
          return content.includes('src="/main.js"');
        } catch (e) {
          return false;
        }
      }
    }
  ];
  
  let passedChecks = 0;
  const totalChecks = checks.length;
  
  checks.forEach((check, index) => {
    process.stdout.write(`${index + 1}. ${check.name}... `);
    
    try {
      const result = check.test();
      if (result) {
        console.log('✅ 通过');
        passedChecks++;
      } else {
        console.log('❌ 失败');
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
    }
  });
  
  console.log(`\n📊 测试结果: ${passedChecks}/${totalChecks} 通过`);
  
  // 显示当前配置
  try {
    const manifestContent = fs.readFileSync('manifest.json', 'utf8');
    const manifest = JSON.parse(manifestContent);
    
    console.log('\n📋 当前 H5 配置:');
    console.log('• Template:', manifest.h5.template || '未设置');
    console.log('• Router Mode:', manifest.h5.router.mode);
    console.log('• Base Path:', manifest.h5.router.base);
    console.log('• Title:', manifest.h5.title);
  } catch (e) {
    console.log('\n❌ 无法读取 H5 配置');
  }
  
  if (passedChecks === totalChecks) {
    console.log('\n🎉 H5 配置修复成功！');
    console.log('\n✨ 修复内容:');
    console.log('• ✅ 设置了正确的 HTML 模板');
    console.log('• ✅ 修复了基础路径配置');
    console.log('• ✅ 确保了资源路径正确');
  } else {
    console.log('\n⚠️  部分配置仍有问题。');
  }
  
  console.log('\n🚀 现在应该可以访问:');
  console.log('• http://localhost:5100/ (根路径)');
  console.log('• http://localhost:5100/#/pages/sys/login/index (首页)');
  
  console.log('\n📋 修复说明:');
  console.log('1. **Template 配置**: 指定了 index.html 作为 H5 模板');
  console.log('2. **Base 路径**: 从 "hdweb" 改为 "/" 避免路径问题');
  console.log('3. **资源加载**: 确保 main.js 等资源能正确加载');
  
  console.log('\n🔧 如果仍有问题:');
  console.log('• 重启 HBuilderX 项目');
  console.log('• 清理缓存: rm -rf unpackage');
  console.log('• 检查浏览器控制台的错误信息');
  console.log('• 查看 Network 标签的资源加载情况');
  
  return passedChecks === totalChecks;
}

// 运行测试
if (require.main === module) {
  const success = testH5ConfigFix();
  process.exit(success ? 0 : 1);
}

module.exports = { testH5ConfigFix };
