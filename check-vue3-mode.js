/**
 * 检查项目是否正确配置为 Vue 3 模式
 */

const fs = require('fs');

function checkVue3Mode() {
  console.log('🔍 检查 Vue 3 模式配置...\n');
  
  const checks = [
    {
      name: 'manifest.json 包含 Vue 3 配置',
      test: () => {
        try {
          const content = fs.readFileSync('manifest.json', 'utf8');
          const manifest = JSON.parse(content);
          return manifest.vueVersion === "3" || manifest.type === "vue3";
        } catch (e) {
          console.error('读取 manifest.json 失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'tsconfig.json 配置简化',
      test: () => {
        try {
          const content = fs.readFileSync('tsconfig.json', 'utf8');
          const config = JSON.parse(content);
          return config.compilerOptions.skipLibCheck === true &&
                 config.compilerOptions.noEmit === true;
        } catch (e) {
          console.error('读取 tsconfig.json 失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'vite.config.js 存在且配置正确',
      test: () => {
        try {
          const content = fs.readFileSync('vite.config.js', 'utf8');
          return content.includes('defineConfig') &&
                 content.includes('uni()') &&
                 content.includes('uview-plus');
        } catch (e) {
          console.error('读取 vite.config.js 失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'package.json 包含 Vue 3 依赖',
      test: () => {
        try {
          const content = fs.readFileSync('package.json', 'utf8');
          const pkg = JSON.parse(content);
          const vue3Version = pkg.dependencies?.vue;
          return vue3Version && vue3Version.startsWith('^3');
        } catch (e) {
          console.error('读取 package.json 失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'main.js 使用 Vue 3 语法',
      test: () => {
        try {
          const content = fs.readFileSync('main.js', 'utf8');
          return content.includes('createSSRApp') &&
                 content.includes('export function createApp');
        } catch (e) {
          console.error('读取 main.js 失败:', e.message);
          return false;
        }
      }
    }
  ];
  
  let passedChecks = 0;
  const totalChecks = checks.length;
  
  checks.forEach((check, index) => {
    process.stdout.write(`${index + 1}. ${check.name}... `);
    
    try {
      const result = check.test();
      if (result) {
        console.log('✅ 通过');
        passedChecks++;
      } else {
        console.log('❌ 失败');
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
    }
  });
  
  console.log(`\n📊 检查结果: ${passedChecks}/${totalChecks} 通过`);
  
  if (passedChecks === totalChecks) {
    console.log('🎉 Vue 3 模式配置正确！');
  } else {
    console.log('⚠️  部分配置有问题，请检查上述失败项。');
  }
  
  console.log('\n📋 如果仍有编译错误，请尝试:');
  console.log('1. 在 HBuilderX 中选择 "运行 -> 运行到浏览器 -> Chrome"');
  console.log('2. 确保 HBuilderX 版本 >= 3.2.0 (支持 Vue 3)');
  console.log('3. 清理项目缓存: 删除 unpackage 文件夹');
  console.log('4. 重新安装依赖: rm -rf node_modules && pnpm install');
  
  if (passedChecks < totalChecks) {
    console.log('\n🔧 修复建议:');
    console.log('• 确保 manifest.json 包含 "vueVersion": "3"');
    console.log('• 简化 tsconfig.json 配置');
    console.log('• 检查 HBuilderX 是否识别为 Vue 3 项目');
  }
  
  return passedChecks === totalChecks;
}

// 运行检查
if (require.main === module) {
  const success = checkVue3Mode();
  process.exit(success ? 0 : 1);
}

module.exports = { checkVue3Mode };
