(this["webpackJsonp"]=this["webpackJsonp"]||[]).push([["chanpin/app-sub-service"],{"0bea":function(t,e,s){"use strict";s.r(e);var i=s("5b43"),a=s("ef62");for(var r in a)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(r);var n=s("828b"),o=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=o.exports},"103a":function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return r})),s.d(e,"a",(function(){return i}));var i={searchView:s("ad50").default,dataNull:s("5e99").default,uImage:s("bbb4").default,uButton:s("2585").default,getMore:s("6e3d").default},a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("view",[s("searchView",{attrs:{placeholderStr:"\u8bf7\u8f93\u5165\u4ea7\u54c1\u540d\u79f0",_i:1},on:{searchViewClickFun:function(e){return t.searchEventsFun(e)}}}),t._$s(2,"i",0==t.list.length)?s("dataNull",{attrs:{src:"/static/img/chahua/dataNullXz.png",title:"\u6682\u65e0\u76f8\u5173\u4ea7\u54c1",title1:"\u8bf7\u6dfb\u52a0\u6216\u8005\u66f4\u6362\u67e5\u8be2\u6761\u4ef6",_i:2}}):s("scroll-view",{style:t._$s(3,"s",{height:t.scrollHeight}),attrs:{"refresher-triggered":t._$s(3,"a-refresher-triggered",t.triggered),_i:3},on:{scrolltolower:t.getChanPinFun,refresherrefresh:t.onRefresh,refresherrestore:t.onRestore}},[t._l(t._$s(4,"f",{forItems:t.list}),(function(e,i,a,r){return s("view",{key:t._$s(4,"f",{forIndex:a,key:i}),attrs:{_i:"4-"+r},on:{click:function(s){return t.cardClick(e)}}},[t._$s("5-"+r,"i",e.cpFmtList&&e.cpFmtList.length>0)?s("view",{staticClass:t._$s("5-"+r,"sc","myCard2"),attrs:{_i:"5-"+r}},[s("view",{staticClass:t._$s("6-"+r,"sc","leftImg"),attrs:{_i:"6-"+r}},[s("u-image",{attrs:{height:"160",width:"160","border-radius":"26",src:e.cpFmtList[0].url,_i:"7-"+r}})],1),s("view",{staticClass:t._$s("8-"+r,"sc","rightView"),attrs:{_i:"8-"+r}},[s("view",{staticClass:t._$s("9-"+r,"sc","cardTopName1"),attrs:{_i:"9-"+r}},[t._v(t._$s("9-"+r,"t0-0",t._s(e.pName)))]),t._$s("10-"+r,"i",e.salesNum||e.stock)?s("view",{staticClass:t._$s("10-"+r,"sc","xlKcClass"),attrs:{_i:"10-"+r}},[t._$s("11-"+r,"i",e.salesNum)?s("text",{attrs:{_i:"11-"+r}},[t._v(t._$s("11-"+r,"t0-0",t._s(e.salesNum)))]):t._e(),t._$s("12-"+r,"i",e.stock)?s("text",{attrs:{_i:"12-"+r}},[t._v(t._$s("12-"+r,"t0-0",t._s(e.stock)))]):t._e()]):t._e(),s("view",{staticClass:t._$s("13-"+r,"sc","cardRow1"),attrs:{_i:"13-"+r}},[s("text",{staticClass:t._$s("14-"+r,"sc","redColor"),attrs:{_i:"14-"+r}},[t._v(t._$s("14-"+r,"t0-0",t._s(e.price)))]),t._v(t._$s("13-"+r,"t1-0",t._s(e.unit)))]),s("view",{staticClass:t._$s("15-"+r,"sc","rowBtn"),attrs:{_i:"15-"+r}},[s("u-button",{staticClass:t._$s("16-"+r,"sc","cpBtn"),attrs:{type:"primary",plain:!0,size:"mini",_i:"16-"+r},on:{click:function(s){return s.stopPropagation(),t.setCpFun(e)}}},[t._v("")]),s("u-button",{staticClass:t._$s("17-"+r,"sc","cpBtn"),attrs:{type:"warning",plain:!0,size:"mini",_i:"17-"+r},on:{click:function(s){return t.cpsxjFun(e,i)}}},[t._v(t._$s("17-"+r,"t0-0",t._s("\u4e0a\u67b6"==e.isSxJ?"\u4e0b\u67b6":"\u4e0a\u67b6")))]),s("u-button",{staticClass:t._$s("18-"+r,"sc","cpBtn"),attrs:{type:"error",plain:!0,size:"mini",_i:"18-"+r},on:{click:function(s){return s.stopPropagation(),t.deleteCpFun(e,i)}}},[t._v("")])],1)])]):s("view",{staticClass:t._$s("19-"+r,"sc","myCard"),attrs:{_i:"19-"+r}},[s("view",{staticClass:t._$s("20-"+r,"sc","cardTopName"),attrs:{_i:"20-"+r}},[t._v(t._$s("20-"+r,"t0-0",t._s(e.pName)))]),s("view",{staticClass:t._$s("21-"+r,"sc","cardRow"),attrs:{_i:"21-"+r}},[s("view"),s("view",[t._v(t._$s("23-"+r,"t0-0",t._s(e.isSxJ)))])]),s("view",{staticClass:t._$s("24-"+r,"sc","cardRow"),attrs:{_i:"24-"+r}},[s("view"),s("view",[s("text",{staticClass:t._$s("27-"+r,"sc","redColor"),attrs:{_i:"27-"+r}},[t._v(t._$s("27-"+r,"t0-0",t._s(e.price)))]),t._v(t._$s("26-"+r,"t1-0",t._s(e.unit)))])]),s("view",{staticClass:t._$s("28-"+r,"sc","cardRow"),attrs:{_i:"28-"+r}},[s("view"),s("view",[t._v(t._$s("30-"+r,"t0-0",t._s(e.cpClassify)))])]),t._$s("31-"+r,"i",e.salesNum)?s("view",{staticClass:t._$s("31-"+r,"sc","cardRow"),attrs:{_i:"31-"+r}},[s("view"),s("view",[t._v(t._$s("33-"+r,"t0-0",t._s(e.salesNum)))])]):t._e(),t._$s("34-"+r,"i",e.stock)?s("view",{staticClass:t._$s("34-"+r,"sc","cardRow"),attrs:{_i:"34-"+r}},[s("view"),s("view",[t._v(t._$s("36-"+r,"t0-0",t._s(e.stock)))])]):t._e(),t._$s("37-"+r,"i",e.describe)?s("view",{staticClass:t._$s("37-"+r,"sc","cardRow"),attrs:{_i:"37-"+r}},[s("view"),s("view",[t._v(t._$s("39-"+r,"t0-0",t._s(e.describe)))])]):t._e(),s("view",{staticClass:t._$s("40-"+r,"sc","rowBtn"),attrs:{_i:"40-"+r}},[s("u-button",{staticClass:t._$s("41-"+r,"sc","cpBtn"),attrs:{type:"primary",plain:!0,size:"mini",_i:"41-"+r},on:{click:function(s){return s.stopPropagation(),t.setCpFun(e)}}},[t._v("")]),s("u-button",{staticClass:t._$s("42-"+r,"sc","cpBtn"),attrs:{type:"warning",plain:!0,size:"mini",_i:"42-"+r},on:{click:function(s){return t.cpsxjFun(e,i)}}},[t._v(t._$s("42-"+r,"t0-0",t._s("\u4e0a\u67b6"==e.isSxJ?"\u4e0b\u67b6":"\u4e0a\u67b6")))]),s("u-button",{staticClass:t._$s("43-"+r,"sc","cpBtn"),attrs:{type:"error",plain:!0,size:"mini",_i:"43-"+r},on:{click:function(s){return s.stopPropagation(),t.deleteCpFun(e,i)}}},[t._v("")])],1)])])})),s("getMore",{staticClass:t._$s(44,"sc","h100"),attrs:{isMore:t.isMore,_i:44}})],2)],1)},r=[]},"11e8":function(t,e,s){"use strict";(function(t){var i=s("47a9"),a=s("3b2d");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(s("5e99")),n=i(s("6e3d")),o=i(s("c943")),c=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!==a(t)&&"function"!==typeof t)return{default:t};var s=l(e);if(s&&s.has(t))return s.get(t);var i={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in t)if("default"!==n&&Object.prototype.hasOwnProperty.call(t,n)){var o=r?Object.getOwnPropertyDescriptor(t,n):null;o&&(o.get||o.set)?Object.defineProperty(i,n,o):i[n]=t[n]}i.default=t,s&&s.set(t,i);return i}(s("8b24"));function l(t){if("function"!==typeof WeakMap)return null;var e=new WeakMap,s=new WeakMap;return(l=function(t){return t?s:e})(t)}var u="",d={components:{skeleton:o.default,getMore:n.default,dataNull:r.default},data:function(){return{customerID:"0",searchFabricGoods:"",cpClassify:"",cpFlId:"",list:[],pageNum:0,scrollLeft:0,enableScroll:!0,tabCurrentIndex:0,tabBars:[],wHeight:623,leftA:0,triggered:!1,freshing:!1,sortObj:{update_date:-1,salesNum:-1,stock:1,create_date:-1},popupShow:!1,sxArr:[],tabList:[{iconPath:"photo-fill",selectedIconPath:"photo-fill",text:"\u540d\u7247",customIcon:!1},{iconPath:"shopping-cart-fill",selectedIconPath:"shopping-cart-fill",text:"\u5546\u57ce",customIcon:!1}],current:1}},onLoad:function(e){u=this,u.pageType=e.type?e.type:"",u.customerID=e.customerid,t("log","----\x3e>>>"+u.customerID," at chanpin/view/shop.vue:175"),uni.getSystemInfoSync().windowWidth,u.wHeight=uni.getSystemInfoSync().windowHeight,u.getClassifyFun()},onShow:function(){},onShareAppMessage:function(e){return"button"===e.from&&t("log",e.target," at chanpin/view/shop.vue:187"),{title:"\u60a8\u597d\uff0c\u8fd9\u662f\u6211\u7684\u5fae\u4fe1\u5c0f\u5546\u57ce\uff0c\u5feb\u6765\u67e5\u770b\u5427\uff01",path:"/chanpin/view/shop?id="+u.cardInfo.u_id}},onShareTimeline:function(){return{title:"\u60a8\u597d\uff0c\u8fd9\u662f\u6211\u7684\u5fae\u4fe1\u5c0f\u5546\u57ce\uff0c\u5feb\u6765\u67e5\u770b\u5427\uff01",imageUrl:"",query:"id="+u.cardInfo.u_id}},methods:{tabbarChangeFun:function(t){0==t&&uni.redirectTo({url:"../../nameCard/view/previewCard?id="+u.userId})},getuserInfoFun:function(e){var s={action:"getUserCrmUid",params:{uid:e}};t("log",s," at chanpin/view/shop.vue:217"),uni.showLoading({title:"\u52a0\u8f7d\u4e2d..."}),crmMyAppUserApi(s).then((function(t){u.getClassifyFun()}))},getClassifyFun:function(){var t=this;uni.request({url:c.default.apiurl+"rest/db/opensql",data:{token:getApp().globalData.Token,format:"json",data:{db_name:getApp().globalData.AppDBName,sql_command_id:"APP.BaseData.GetFabricTypeData",params:[]}},success:function(e){t.triggered=!1;var s=e.data.data;if(s){for(var i=0;i<s.length;i++)s[i].pageIndex=1,s[i].arr=[],s[i].isMore=!1,s[i].triggered=!1;s[0].loaded=!0,u.tabBars=s,u.cpClassify=s[0].FabricTypeName,u.cpFlId=s[0].FabricTypeID,u.getChanPinFun()}}})},getChanPinFun:function(){var e=u.leftA,s=-1,i=this.searchFabricGoods;""!=i&&(s=0),t("log","-----\x3e>>"+s+"<<<>>"+this.searchFabricGoods," at chanpin/view/shop.vue:305"),uni.request({url:c.default.apiurl+"rest/db/opensql",data:{token:getApp().globalData.Token,format:"json",data:{db_name:getApp().globalData.AppDBName,sql_command_id:"APP.StoreGoods.GetStoreGoodsMasterFabricGoods",params:[{name:"FTypeID",value:u.tabBars[e].FabricTypeID},{name:"OTypeID",value:s},{name:"FGNo",value:"%"+this.searchFabricGoods+"%"},{name:"FGName",value:"%"+this.searchFabricGoods+"%"}]}},success:function(t){var s=t.data.data,i=u.tabBars[e];u.tabBars[e].arr;i.triggered&&(i.triggered=!1,u.freshing=!1),20==s.length?(i.isMore=!0,i.pageIndex+=1):i.isMore=!1,i.arr=s,i.isjz=!0,u.$set(u.tabBars,e,i)}})},selectLeftFl:function(e){this.leftA=e,this.tabCurrentIndex=e;var s=this.tabBars[e];!0!==s.loaded&&(t("log","-----------\x3e"+this.tabCurrentIndex," at chanpin/view/shop.vue:407"),this.getChanPinFun(),s.loaded=!0)},loadMore:function(t){this.tabBars[this.leftA].isMore&&this.getChanPinFun()},onRefresh:function(){var t=this.tabBars[this.leftA];this.freshing||(this.freshing=!0,t.triggered||(t.triggered=!0),t.isMore=!0,t.pageIndex=1,this.$set(this.tabBars,this.leftA,t),this.getChanPinFun())},onRestore:function(){var t=this.tabBars[this.leftA];t.triggered=!1,this.$set(this.tabBars,this.leftA,t)},cardClick:function(e){""!==u.pageType?(uni.$emit("chanpinBindFun",e),uni.navigateBack()):(t("log",">>>>>"+this.customerID+"<<<<<<<<<"," at chanpin/view/shop.vue:440"),uni.$cpDetail=e,uni.navigateTo({url:"./cpDetail?customerid="+this.customerID+"&obj="+encodeURIComponent(JSON.stringify(e))}))},sxItemClickFun:function(t,e){for(var s=u.sxArr[t],i=u.sxArr,a=0;a<i.length;a++)i[a].isPx&&(i[a].current="");s.current=e,u.$set(u.sxArr,t,s)},resetFun:function(){for(var t=u.sxArr,e=0;e<t.length;e++)t[e].current="";u.sxArr=t},confirmFun:function(){u.popupShow=!1;for(var e=u.sxArr,s=0;s<e.length;s++)if(e[s].isPx&&""!==e[s].current){t("log",e[s]," at chanpin/view/shop.vue:472");var i={};"create_date"==e[s].field&&(i.create_date=1==e[s].current?1:-1),"update_date"==e[s].field&&(i.update_date=1==e[s].current?1:-1),"salesNum"==e[s].field&&(i.salesNum=1==e[s].current?1:-1),"stock"==e[s].field&&(i.stock=1==e[s].current?1:-1),u.sortObj=i;break}u.pageIndex=1,u.isMore=!0,u.getChanPinFun()}}};e.default=d}).call(this,s("f3b9")["default"])},1839:function(t,e,s){"use strict";(function(t){var i=s("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(s("5e99")),r=i(s("6e3d")),n=i(s("c943")),o="",c={components:{skeleton:n.default,getMore:r.default,dataNull:a.default},data:function(){return{cpClassify:"",cpFlId:"",list:[],pageNum:0,scrollLeft:0,enableScroll:!0,tabCurrentIndex:0,tabBars:[],wHeight:623,leftA:0,triggered:!1,freshing:!1,sortObj:{update_date:-1,salesNum:-1,stock:1,create_date:-1},popupShow:!1,sxArr:[{title:"\u521b\u5efa\u65e5\u671f\u6392\u5e8f",field:"create_date",current:"",isPx:!0,arr:["\u964d\u5e8f","\u5347\u5e8f"]},{title:"\u4fee\u6539\u65e5\u671f\u6392\u5e8f",field:"update_date",current:"",isPx:!0,arr:["\u964d\u5e8f","\u5347\u5e8f"]},{title:"\u9500\u91cf\u6392\u5e8f",field:"salesNum",current:"",isPx:!0,arr:["\u964d\u5e8f","\u5347\u5e8f"]},{title:"\u5e93\u5b58\u6392\u5e8f",field:"stock",current:"",isPx:!0,arr:["\u964d\u5e8f","\u5347\u5e8f"]}],tabList:[{iconPath:"photo-fill",selectedIconPath:"photo-fill",text:"\u540d\u7247",customIcon:!1},{iconPath:"shopping-cart-fill",selectedIconPath:"shopping-cart-fill",text:"\u5546\u57ce",customIcon:!1}],current:1}},onLoad:function(t){o=this,o.pageType=t.type?t.type:"",uni.getSystemInfoSync().windowWidth,o.wHeight=uni.getSystemInfoSync().windowHeight,t.id?o.userId=t.id:(o.userId=uni.$userInfo._id,o.getClassifyFun())},onShow:function(){uni.$userInfo=uni.getStorageSync("userInfo")||{},uni.$userInfo&&uni.$userInfo._id&&o.getClassifyFun()},onShareAppMessage:function(e){return"button"===e.from&&t("log",e.target," at pages/basedata/chanpin/shop.vue:226"),{title:"\u60a8\u597d\uff0c\u8fd9\u662f\u6211\u7684\u5fae\u4fe1\u5c0f\u5546\u57ce\uff0c\u5feb\u6765\u67e5\u770b\u5427\uff01",path:"/chanpin/view/shop?id="+o.cardInfo.u_id}},onShareTimeline:function(){return{title:"\u60a8\u597d\uff0c\u8fd9\u662f\u6211\u7684\u5fae\u4fe1\u5c0f\u5546\u57ce\uff0c\u5feb\u6765\u67e5\u770b\u5427\uff01",imageUrl:"",query:"id="+o.cardInfo.u_id}},methods:{tabbarChangeFun:function(t){0==t&&uni.redirectTo({url:"../../nameCard/view/previewCard?id="+o.userId})},getuserInfoFun:function(e){var s={action:"getUserCrmUid",params:{uid:e}};t("log",s," at pages/basedata/chanpin/shop.vue:256"),uni.showLoading({title:"\u52a0\u8f7d\u4e2d..."}),crmMyAppUserApi(s).then((function(t){o.getClassifyFun()}))},getClassifyFun:function(){uni.showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0});crmCpClassifyApi({action:"getFlList",params:{matchObj:{isShow:!0}}}).then((function(e){t("log",e," at pages/basedata/chanpin/shop.vue:281");var s=e.result.data;if(t("log",s," at pages/basedata/chanpin/shop.vue:283"),s){for(var i=0;i<s.length;i++)s[i].pageIndex=1,s[i].arr=[],s[i].isMore=!1,s[i].triggered=!1;s[0].loaded=!0,o.tabBars=s,o.cpClassify=s[0].flName,o.cpFlId=s[0]._id,o.getChanPinFun()}}))},getChanPinFun:function(){uni.showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0});var e=o.leftA,s={matchObj:{cpFlId:o.tabBars[e]._id,isSxJ:!0},pageIndex:o.tabBars[e].pageIndex,sortObj:o.sortObj},i=s.pageIndex,a={action:"getCpList",params:s};t("log",a," at pages/basedata/chanpin/shop.vue:320"),crmChanpinApi(a).then((function(t){var s=t.result.data,a=o.tabBars[e],r=o.tabBars[e].arr;a.triggered&&(a.triggered=!1,o.freshing=!1),20==s.length?(a.isMore=!0,a.pageIndex+=1):a.isMore=!1,1==i?a.arr=s:(r=r.concat(s),a.arr=r),a.isjz=!0,o.$set(o.tabBars,e,a)}))},selectLeftFl:function(t){this.leftA=t,this.tabCurrentIndex=t;var e=this.tabBars[t];!0!==e.loaded&&(uni.showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0}),this.getChanPinFun(),e.loaded=!0)},loadMore:function(t){this.tabBars[this.leftA].isMore&&this.getChanPinFun()},onRefresh:function(){var t=this.tabBars[this.leftA];this.freshing||(this.freshing=!0,t.triggered||(t.triggered=!0),t.isMore=!0,t.pageIndex=1,this.$set(this.tabBars,this.leftA,t),this.getChanPinFun())},onRestore:function(){var t=this.tabBars[this.leftA];t.triggered=!1,this.$set(this.tabBars,this.leftA,t)},cardClick:function(t){""!==o.pageType?(uni.$emit("chanpinBindFun",t),uni.navigateBack()):(uni.$cpDetail=t,uni.navigateTo({url:"./cpDetail?type=client"}))},sxItemClickFun:function(t,e){for(var s=o.sxArr[t],i=o.sxArr,a=0;a<i.length;a++)i[a].isPx&&(i[a].current="");s.current=e,o.$set(o.sxArr,t,s)},resetFun:function(){for(var t=o.sxArr,e=0;e<t.length;e++)t[e].current="";o.sxArr=t},confirmFun:function(){o.popupShow=!1;for(var e=o.sxArr,s=0;s<e.length;s++)if(e[s].isPx&&""!==e[s].current){t("log",e[s]," at pages/basedata/chanpin/shop.vue:418");var i={};"create_date"==e[s].field&&(i.create_date=1==e[s].current?1:-1),"update_date"==e[s].field&&(i.update_date=1==e[s].current?1:-1),"salesNum"==e[s].field&&(i.salesNum=1==e[s].current?1:-1),"stock"==e[s].field&&(i.stock=1==e[s].current?1:-1),o.sortObj=i;break}o.pageIndex=1,o.isMore=!0,o.getChanPinFun()}}};e.default=c}).call(this,s("f3b9")["default"])},"198c":function(t,e,s){"use strict";s.r(e);var i=s("103a"),a=s("7804");for(var r in a)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(r);var n=s("828b"),o=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=o.exports},"299a":function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return r})),s.d(e,"a",(function(){return i}));var i={uInput:s("31df").default,uIcon:s("f7a4").default,uUpload:s("f954").default,uButton:s("2585").default,uSelect:s("c0b3").default},a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("view",[s("view",{staticClass:t._$s(1,"sc","flex-white-plr26 ptb10 mt32 bdb_f5"),attrs:{_i:1}},[s("text",{staticClass:t._$s(2,"sc","mr26"),attrs:{_i:2}},[s("text",{staticClass:t._$s(3,"sc","redXingh"),attrs:{_i:3}})]),s("u-input",{attrs:{placeholder:"\u8bf7\u5728\u6b64\u8f93\u5165\u4ea7\u54c1\u540d\u79f0",_i:4},model:{value:t._$s(4,"v-model",t.pName),callback:function(e){t.pName=e},expression:"pName"}})],1),s("view",{staticClass:t._$s(5,"sc","flex-white-plr26 ptb10 bdb_f5"),attrs:{_i:5},on:{click:function(e){return t.pickerSelectFun("\u4ea7\u54c1\u5355\u4f4d")}}},[s("text",{staticClass:t._$s(6,"sc","mr26"),attrs:{_i:6}},[s("text",{staticClass:t._$s(7,"sc","redXingh"),attrs:{_i:7}})]),s("u-input",{attrs:{placeholder:"\u8bf7\u5728\u6b64\u8f93\u5165\u4ea7\u54c1\u5355\u4f4d",_i:8},model:{value:t._$s(8,"v-model",t.unit),callback:function(e){t.unit=e},expression:"unit"}})],1),s("view",{staticClass:t._$s(9,"sc","flex-white-plr26 ptb10 bdb_f5"),attrs:{_i:9}},[s("text",{staticClass:t._$s(10,"sc","mr26"),attrs:{_i:10}},[s("text",{staticClass:t._$s(11,"sc","redXingh"),attrs:{_i:11}})]),s("u-input",{attrs:{type:"number",placeholder:"\u8bf7\u5728\u6b64\u8f93\u5165\u4ea7\u54c1\u4ef7\u683c",_i:12},model:{value:t._$s(12,"v-model",t.price),callback:function(e){t.price=e},expression:"price"}})],1),s("view",{staticClass:t._$s(13,"sc","flex-white-plr26 ptb20 bdb_f5"),attrs:{_i:13},on:{click:function(e){return t.pickerSelectFun("\u4ea7\u54c1\u7c7b\u578b")}}},[s("text",{staticClass:t._$s(14,"sc","mr26"),attrs:{_i:14}},[s("text",{staticClass:t._$s(15,"sc","redXingh"),attrs:{_i:15}})]),s("view",{class:t._$s(16,"c",t.flName?"":"cBlack"),attrs:{_i:16}},[t._v(t._$s(16,"t0-0",t._s(t.flName?t.flName:"\u8bf7\u9009\u62e9"))),s("u-icon",{staticClass:t._$s(17,"sc","ml26"),attrs:{name:"arrow-right",size:"40",color:"#888888",_i:17}})],1)]),s("view",{staticClass:t._$s(18,"sc","flex-white-plr26 ptb20 bdb_f5"),attrs:{_i:18},on:{click:function(e){return t.pickerSelectFun("\u4e0a\u4e0b\u67b6")}}},[s("text",{staticClass:t._$s(19,"sc","mr26"),attrs:{_i:19}},[s("text",{staticClass:t._$s(20,"sc","redXingh"),attrs:{_i:20}})]),s("view",{class:t._$s(21,"c",t.isSxJ?"":"cBlack"),attrs:{_i:21}},[t._v(t._$s(21,"t0-0",t._s(t.isSxJ?t.isSxJ:"\u8bf7\u9009\u62e9"))),s("u-icon",{staticClass:t._$s(22,"sc","ml26"),attrs:{name:"arrow-right",size:"40",color:"#888888",_i:22}})],1)]),s("view",{staticClass:t._$s(23,"sc","flex-white-plr26 ptb10 bdb_f5"),attrs:{_i:23}},[s("text",{staticClass:t._$s(24,"sc","mr26"),attrs:{_i:24}}),s("u-input",{attrs:{type:"number",placeholder:"\u8bf7\u5728\u6b64\u8f93\u5165\u4ea7\u54c1\u9500\u91cf",_i:25},model:{value:t._$s(25,"v-model",t.salesNum),callback:function(e){t.salesNum=e},expression:"salesNum"}})],1),s("view",{staticClass:t._$s(26,"sc","flex-white-plr26 ptb10 bdb_f5"),attrs:{_i:26}},[s("text",{staticClass:t._$s(27,"sc","mr26"),attrs:{_i:27}}),s("u-input",{attrs:{type:"number",placeholder:"\u8bf7\u5728\u6b64\u8f93\u5165\u4ea7\u54c1\u5e93\u5b58",_i:28},model:{value:t._$s(28,"v-model",t.stock),callback:function(e){t.stock=e},expression:"stock"}})],1),s("view",{staticClass:t._$s(29,"sc","flex-white-plr26 ptb10 bdb_f5"),attrs:{_i:29}},[s("text",{staticClass:t._$s(30,"sc","mr26"),attrs:{_i:30}}),s("u-input",{attrs:{placeholder:"\u8bf7\u5728\u6b64\u8f93\u5165\u4ea7\u54c1\u7f16\u7801",_i:31},model:{value:t._$s(31,"v-model",t.code),callback:function(e){t.code=e},expression:"code"}}),s("u-icon",{staticClass:t._$s(32,"sc","ml26"),attrs:{name:"scan",size:"44",color:"#007aff",_i:32},on:{click:t.scanFun}})],1),s("view",{staticClass:t._$s(33,"sc","flex-white-plr26-column ptb20 mt32 bdb_f5"),attrs:{_i:33}},[s("view",[s("text")]),s("u-input",{attrs:{type:"textarea",border:!0,height:100,"auto-height":!0,_i:36},model:{value:t._$s(36,"v-model",t.describe),callback:function(e){t.describe=e},expression:"describe"}})],1),s("view",{staticClass:t._$s(37,"sc","flex-white-plr26-column ptb20 mt32 bdb_f5"),attrs:{_i:37}},[s("view",[s("text"),s("u-icon",{attrs:{name:"plus-circle-fill",color:"#007aff",size:"50",_i:40},on:{click:t.addCsFun}})],1),t._l(t._$s(41,"f",{forItems:t.cpCsList}),(function(e,i,a,r){return s("view",{key:t._$s(41,"f",{forIndex:a,key:i}),staticClass:t._$s("41-"+r,"sc","canshuInputRow"),attrs:{_i:"41-"+r}},[s("view",{staticClass:t._$s("42-"+r,"sc","csInput1"),attrs:{_i:"42-"+r}},[s("u-input",{attrs:{border:!0,placeholder:"\u53c2\u6570\u540d\u79f0",_i:"43-"+r},model:{value:t._$s("43-"+r,"v-model",e.csName),callback:function(s){t.$set(e,"csName",s)},expression:"item.csName"}})],1),s("view",{staticClass:t._$s("44-"+r,"sc","csInput2"),attrs:{_i:"44-"+r}},[s("u-input",{attrs:{border:!0,placeholder:"\u8bf7\u5728\u6b64\u8f93\u5165\u53c2\u6570\u4ecb\u7ecd",_i:"45-"+r},model:{value:t._$s("45-"+r,"v-model",e.csJieShao),callback:function(s){t.$set(e,"csJieShao",s)},expression:"item.csJieShao"}})],1),s("u-icon",{attrs:{name:"close-circle-fill",size:"50",color:"#ff0000",_i:"46-"+r},on:{click:function(e){return t.deleteCpCsFun(i)}}})],1)}))],2),s("view",{staticClass:t._$s(47,"sc","flex-white-plr26-column ptb20 mt32"),attrs:{_i:47}},[s("view"),s("u-upload",{attrs:{"size-type":t.siziType,"show-progress":!1,"file-list":t.cpFmtList,"max-size":5242880,"max-count":"1","auto-upload":!1,_i:49},on:{"on-choose-complete":t.uploadFmChangeFun,"on-remove":t.fmtRemoeFun}})],1),s("view",{staticClass:t._$s(50,"sc","flex-white-plr26-column ptb20 mt32"),attrs:{_i:50}},[s("view"),s("u-upload",{attrs:{"size-type":t.siziType,"show-progress":!1,"file-list":t.cpLbtList,"max-size":5242880,"max-count":"5","auto-upload":!1,_i:52},on:{"on-choose-complete":t.uploadLbtChangeFun,"on-list-change":t.fileChangeFun,"on-remove":t.lbtRemoeFun}})],1),s("view",{staticClass:t._$s(53,"sc","flex-white-plr26-column ptb20 mt32"),attrs:{_i:53}},[s("view"),s("u-upload",{attrs:{"size-type":t.siziType,"show-progress":!1,"file-list":t.cpXqtList,"max-size":5242880,"max-count":"9","auto-upload":!1,_i:55},on:{"on-choose-complete":t.uploadXqChangeFun,"on-remove":t.xqtRemoeFun}})],1),s("view",{staticClass:t._$s(56,"sc","submitView"),attrs:{_i:56}},[t._$s(57,"i",!t.pageType)?s("u-button",{staticClass:t._$s(57,"sc","submitBtn"),attrs:{type:"primary",ripple:!0,"ripple-bg-color":"#909399",_i:57},on:{click:t.addXgFun}},[t._v("")]):t._e(),t._$s(58,"i","update"==t.pageType)?s("u-button",{staticClass:t._$s(58,"sc","submitBtn"),attrs:{type:"primary",ripple:!0,"ripple-bg-color":"#909399",_i:58},on:{click:t.addXgFun}},[t._v("")]):t._e()],1),s("u-select",{attrs:{list:t.selectList,_i:59},on:{confirm:t.selectConfirmFun},model:{value:t._$s(59,"v-model",t.selectShow),callback:function(e){t.selectShow=e},expression:"selectShow"}}),t._$s(60,"i",t.ppImgShow)?s("uploadImg",{attrs:{ppList:t.ppList,ppUrls:t.ppUrls,_i:60},on:{confirmPopupFun:t.confirmPopupFun}}):t._e()],1)},r=[]},3673:function(t,e,s){"use strict";s.r(e);var i=s("8895"),a=s.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"3c2c":function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return r})),s.d(e,"a",(function(){return i}));var i={dataNull:s("5e99").default,uSwitch:s("1b90").default,addBtn:s("d66d").default,uPopup:s("7942").default},a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("view",[t._$s(1,"i",0==t.list.length)?s("dataNull",{attrs:{src:"/static/img/chahua/dataNullXz.png",title:"\u6682\u65e0\u76f8\u5173\u5206\u7c7b",title1:"\u70b9\u51fb\u6dfb\u52a0\u6309\u94ae\u6dfb\u52a0\u76f8\u5173\u5206\u7c7b",_i:1}}):s("view",{staticClass:t._$s(2,"sc","content"),attrs:{_i:2}},t._l(t._$s(3,"f",{forItems:t.list}),(function(e,i,a,r){return s("view",{key:t._$s(3,"f",{forIndex:a,key:i}),staticClass:t._$s("3-"+r,"sc","row"),attrs:{_i:"3-"+r}},[s("text",{staticClass:t._$s("4-"+r,"sc","text"),attrs:{_i:"4-"+r}},[t._v(t._$s("4-"+r,"t0-0",t._s(e.flName)))]),s("view",{staticClass:t._$s("5-"+r,"sc","rightMenuRow"),attrs:{_i:"5-"+r}},[s("u-switch",{attrs:{"inactive-color":"#f00",_i:"6-"+r},on:{change:function(s){return t.changeClassFun(e)}},model:{value:t._$s("6-"+r,"v-model",e.isShow),callback:function(s){t.$set(e,"isShow",s)},expression:"row.isShow"}}),s("view",{staticClass:t._$s("7-"+r,"sc","menuBtn primaryBtn"),attrs:{_i:"7-"+r},on:{click:function(s){return t.updateNameFun(e)}}}),s("view",{staticClass:t._$s("8-"+r,"sc","menuBtn errorBtn"),attrs:{_i:"8-"+r},on:{click:function(s){return t.deleteFun(e)}}})],1)])})),0),s("view",{attrs:{_i:9},on:{click:function(e){t.popupShow=!0}}},[s("addBtn",{attrs:{_i:10}})],1),s("u-popup",{attrs:{mode:"center","border-radius":"14",width:"500rpx",_i:11},model:{value:t._$s(11,"v-model",t.popupShow),callback:function(e){t.popupShow=e},expression:"popupShow"}},[s("view",{staticClass:t._$s(12,"sc","popupTitle"),attrs:{_i:12}}),s("view",{staticClass:t._$s(13,"sc","popupInput"),attrs:{_i:13}},[s("text"),s("input",{directives:[{name:"model",rawName:"v-model",value:t.flName,expression:"flName"}],staticClass:t._$s(15,"sc","uInput"),attrs:{_i:15},domProps:{value:t._$s(15,"v-model",t.flName)},on:{input:function(e){e.target.composing||(t.flName=e.target.value)}}})]),s("view",{staticClass:t._$s(16,"sc","popupInput"),attrs:{_i:16}},[s("text"),s("input",{directives:[{name:"model",rawName:"v-model",value:t.sort,expression:"sort"}],staticClass:t._$s(18,"sc","uInput"),attrs:{_i:18},domProps:{value:t._$s(18,"v-model",t.sort)},on:{input:function(e){e.target.composing||(t.sort=e.target.value)}}})]),s("view",{staticClass:t._$s(19,"sc","btnRow"),attrs:{_i:19}},[s("view",{staticClass:t._$s(20,"sc","popupBtn errorBtn"),attrs:{_i:20},on:{click:function(e){t.popupShow=!1}}}),s("view",{staticClass:t._$s(21,"sc","popupBtn primaryBtn"),attrs:{_i:21},on:{click:t.addClassifyFun}})])]),s("u-popup",{attrs:{mode:"center","border-radius":"14",width:"500rpx",_i:22},model:{value:t._$s(22,"v-model",t.popupXgShow),callback:function(e){t.popupXgShow=e},expression:"popupXgShow"}},[s("view",{staticClass:t._$s(23,"sc","popupTitle"),attrs:{_i:23}}),s("view",{staticClass:t._$s(24,"sc","popupInput"),attrs:{_i:24}},[s("text"),s("input",{directives:[{name:"model",rawName:"v-model",value:t.flName,expression:"flName"}],staticClass:t._$s(26,"sc","uInput"),attrs:{_i:26},domProps:{value:t._$s(26,"v-model",t.flName)},on:{input:function(e){e.target.composing||(t.flName=e.target.value)}}})]),s("view",{staticClass:t._$s(27,"sc","popupInput"),attrs:{_i:27}},[s("text"),s("input",{directives:[{name:"model",rawName:"v-model",value:t.sort,expression:"sort"}],staticClass:t._$s(29,"sc","uInput"),attrs:{_i:29},domProps:{value:t._$s(29,"v-model",t.sort)},on:{input:function(e){e.target.composing||(t.sort=e.target.value)}}})]),s("view",{staticClass:t._$s(30,"sc","btnRow"),attrs:{_i:30}},[s("view",{staticClass:t._$s(31,"sc","popupBtn errorBtn"),attrs:{_i:31},on:{click:function(e){t.popupXgShow=!1}}}),s("view",{staticClass:t._$s(32,"sc","popupBtn primaryBtn"),attrs:{_i:32},on:{click:t.updateConfirmFun}})])])],1)},r=[]},"3e01":function(t,e,s){"use strict";s.r(e);var i=s("3e30"),a=s("497d");for(var r in a)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(r);var n=s("828b"),o=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=o.exports},"3e30":function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return r})),s.d(e,"a",(function(){return i}));var i={uSearch:s("5600").default,skeleton:s("c943").default,uImage:s("bbb4").default,getMore:s("6e3d").default,dataNull:s("5e99").default},a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("view",[s("view",{staticClass:t._$s(1,"sc","top-box"),attrs:{_i:1}},[s("u-search",{attrs:{placeholder:"\u8bf7\u8f93\u5165\u67e5\u8be2\u7f16\u53f7\u6216\u540d\u79f0",_i:2},on:{search:t.getChanPinFun,custom:t.getChanPinFun},model:{value:t._$s(2,"v-model",t.searchFabricGoods),callback:function(e){t.searchFabricGoods=e},expression:"searchFabricGoods"}})],1),s("view",{staticClass:t._$s(3,"sc","scrollF"),style:t._$s(3,"s",{height:t.wHeight-50+"px"}),attrs:{_i:3}},[s("scroll-view",{staticClass:t._$s(4,"sc","leftScrollV"),attrs:{_i:4}},t._l(t._$s(5,"f",{forItems:t.tabBars}),(function(e,i,a,r){return s("view",{key:t._$s(5,"f",{forIndex:a,key:i}),staticClass:t._$s("5-"+r,"sc","leftCardView"),attrs:{_i:"5-"+r}},[s("view",{staticClass:t._$s("6-"+r,"sc","leftCard"),class:t._$s("6-"+r,"c",{leftActive:t.leftA==i}),attrs:{_i:"6-"+r},on:{click:function(e){return t.selectLeftFl(i)}}},[t._v(t._$s("6-"+r,"t0-0",t._s(e.FabricTypeName)))])])})),0),s("view",{staticClass:t._$s(7,"sc","rightSv"),attrs:{_i:7}},[t._$s(8,"i",t.tabBars.length>0)?s("view",{attrs:{_i:8}},t._l(t._$s(9,"f",{forItems:t.tabBars}),(function(e,i,a,r){return s("scroll-view",{directives:[{name:"show",rawName:"v-show",value:t._$s("9-"+r,"v-show",t.leftA==i),expression:"_$s((\"9-\"+$31),'v-show',leftA == iii)"}],key:t._$s(9,"f",{forIndex:a,key:i}),attrs:{"refresher-triggered":t._$s("9-"+r,"a-refresher-triggered",e.triggered),_i:"9-"+r},on:{scrolltolower:t.loadMore,refresherrefresh:t.onRefresh,refresherrestore:t.onRestore}},[t._$s("10-"+r,"i",!e.isjz)?s("skeleton",{attrs:{SkelttionType:"classify",_i:"10-"+r}}):s("view",{attrs:{_i:"11-"+r}},[t._$s("12-"+r,"i",e.arr.length>0)?s("view",{attrs:{_i:"12-"+r}},[t._l(t._$s("13-"+r,"f",{forItems:e.arr}),(function(e,i,a,n){return s("view",{key:t._$s("13-"+r,"f",{forIndex:a,key:i}),attrs:{_i:"13-"+r+"-"+n},on:{click:function(s){return t.cardClick(e)}}},[t._$s("14-"+r+"-"+n,"i",e.cpFmtList&&e.cpFmtList.length>0)?s("view",{staticClass:t._$s("14-"+r+"-"+n,"sc","myCard2"),attrs:{_i:"14-"+r+"-"+n}},[s("view",{staticClass:t._$s("15-"+r+"-"+n,"sc","leftImg"),attrs:{_i:"15-"+r+"-"+n}},[s("u-image",{attrs:{height:"160",width:"160",src:e.cpFmtList[0].url,_i:"16-"+r+"-"+n}})],1),s("view",{staticClass:t._$s("17-"+r+"-"+n,"sc","rightView"),attrs:{_i:"17-"+r+"-"+n}},[s("view",{staticClass:t._$s("18-"+r+"-"+n,"sc","cardTopName1"),attrs:{_i:"18-"+r+"-"+n}},[t._v(t._$s("18-"+r+"-"+n,"t0-0",t._s(e.pName)))]),t._$s("19-"+r+"-"+n,"i",e.salesNum||e.stock)?s("view",{staticClass:t._$s("19-"+r+"-"+n,"sc","xlKcClass"),attrs:{_i:"19-"+r+"-"+n}},[t._$s("20-"+r+"-"+n,"i",e.salesNum)?s("text",{attrs:{_i:"20-"+r+"-"+n}},[t._v(t._$s("20-"+r+"-"+n,"t0-0",t._s(e.salesNum)))]):t._e(),t._$s("21-"+r+"-"+n,"i",e.stock)?s("text",{attrs:{_i:"21-"+r+"-"+n}},[t._v(t._$s("21-"+r+"-"+n,"t0-0",t._s(e.stock)))]):t._e()]):t._e(),s("view",{staticClass:t._$s("22-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"22-"+r+"-"+n}},[s("text",{staticClass:t._$s("23-"+r+"-"+n,"sc","redColor"),attrs:{_i:"23-"+r+"-"+n}},[t._v(t._$s("23-"+r+"-"+n,"t0-0",t._s(e.price)))]),t._v(t._$s("22-"+r+"-"+n,"t1-0",t._s(e.unit)))])])]):s("view",{staticClass:t._$s("24-"+r+"-"+n,"sc","myCard1"),attrs:{_i:"24-"+r+"-"+n}},[s("view",{staticClass:t._$s("25-"+r+"-"+n,"sc","cardTopName"),attrs:{_i:"25-"+r+"-"+n}},[t._v(t._$s("25-"+r+"-"+n,"t0-0",t._s(e.FabricGoodsNo)))]),s("view",{staticClass:t._$s("26-"+r+"-"+n,"sc","cardTopName"),attrs:{_i:"26-"+r+"-"+n}},[t._v(t._$s("26-"+r+"-"+n,"t0-0",t._s(e.FabricGoodsName)))]),s("view",{staticClass:t._$s("27-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"27-"+r+"-"+n}},[s("view",[t._v(t._$s("28-"+r+"-"+n,"t0-0",t._s(e.GoodsWidth)))]),t._$s("29-"+r+"-"+n,"i",e.GoodsWeight)?s("view"):t._e(),s("view",[t._v(t._$s("30-"+r+"-"+n,"t0-0",t._s(e.GoodsWeight)))])]),s("view",{staticClass:t._$s("31-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"31-"+r+"-"+n}},[s("view",[t._v(t._$s("32-"+r+"-"+n,"t0-0",t._s(e.StoreName)))])]),s("view",{staticClass:t._$s("33-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"33-"+r+"-"+n}},[s("view",[t._v(t._$s("34-"+r+"-"+n,"t0-0",t._s(e.OwnerName)))])]),s("view",{staticClass:t._$s("35-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"35-"+r+"-"+n}},[s("view",[t._v(t._$s("36-"+r+"-"+n,"t0-0",t._s(e.GoodsGradeName)))])]),s("view",{staticClass:t._$s("37-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"37-"+r+"-"+n}},[s("view",[t._v(t._$s("38-"+r+"-"+n,"t0-0",t._s(e.GoodsRemark)))])]),s("view",{staticClass:t._$s("39-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"39-"+r+"-"+n}},[s("view",{staticClass:t._$s("40-"+r+"-"+n,"sc","mr26"),attrs:{_i:"40-"+r+"-"+n}},[t._v(t._$s("40-"+r+"-"+n,"t0-0",t._s(e.Roll)))]),s("view",{staticClass:t._$s("41-"+r+"-"+n,"sc","mr26"),attrs:{_i:"41-"+r+"-"+n}},[t._v(t._$s("41-"+r+"-"+n,"t0-0",t._s(e.Qty))+t._$s("41-"+r+"-"+n,"t0-1",t._s(e.UnitName)))])]),s("view",{staticClass:t._$s("42-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"42-"+r+"-"+n}},[s("view",{staticClass:t._$s("43-"+r+"-"+n,"sc","mr26"),attrs:{_i:"43-"+r+"-"+n}},[t._v(t._$s("43-"+r+"-"+n,"t0-0",t._s(e.AllocatedRoll)))]),s("view",{staticClass:t._$s("44-"+r+"-"+n,"sc","mr26"),attrs:{_i:"44-"+r+"-"+n}},[t._v(t._$s("44-"+r+"-"+n,"t0-0",t._s(e.UseRoll)))])])])])})),s("getMore",{attrs:{isMore:t.tabBars[t.tabCurrentIndex].isMore,_i:"45-"+r}}),s("view")],2):s("dataNull",{attrs:{src:"/static/img/chahua/dataNullXz.png",title:"\u6682\u65e0\u76f8\u5173\u4ea7\u54c1",title1:"\u8bf7\u5148\u6dfb\u52a0",_i:"47-"+r}})],1)],1)})),0):s("dataNull",{attrs:{src:"/static/img/chahua/dataNullXz.png",title:"\u6682\u65e0\u76f8\u5173\u4ea7\u54c1",_i:48}})],1)])])},r=[]},"497d":function(t,e,s){"use strict";s.r(e);var i=s("11e8"),a=s.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"565a":function(t,e,s){"use strict";var i=s("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;s("0ea2"),s("d54d");var a=i(s("5e01")),r=s("eae8"),n="",o={components:{uploadImg:a.default},data:function(){return{selectShow:!1,selectList:[],selectType:"",pName:"",price:"",salesNum:"",stock:"",code:"",flName:"",cpFlId:"",unit:"",isSxJ:"\u4e0a\u67b6",describe:"",action:"http://www.example.com/upload",fileList:[],classify:[],cpLbt:[],cpXqt:[],cpFmt:[],cpLbtList:[],cpXqtList:[],cpFmtList:[],cpCsList:[],pageType:"",siziType:["compressed"],ppImgShow:!1,uploadType:"",ppList:[],ppUrls:[],xgCpInfo:{}}},onLoad:function(t){n=this,"update"==t.type&&(uni.setNavigationBarTitle({title:"\u4fee\u6539\u4ea7\u54c1\u4fe1\u606f"}),n.pageType=t.type,n.setDataFun()),n.getClassifyFun()},methods:{getClassifyFun:function(){uni.showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0});(0,r.crmCpClassifyApi)({action:"getFlList",params:{matchObj:{isShow:!0}}}).then((function(t){var e=t.result.data;e&&e.length>0?("update"!=n.pageType&&(n.flName=e[0].flName,n.cpFlId=e[0]._id),n.classify=e):uni.showModal({title:"\u63d0\u793a",content:"\u5f53\u524d\u6682\u672a\u8bbe\u7f6e\u4ea7\u54c1\u5206\u7c7b\uff0c\u8bf7\u5148\u6dfb\u52a0\u4ea7\u54c1\u5206\u7c7b\uff01",success:function(t){t.confirm&&uni.redirectTo({url:"./classify?pageType=add"})}})}))},chooseAddrFun:function(){uni.chooseLocation({success:function(t){n.gjAddr=t.address}})},pickerSelectFun:function(t){if("\u4ea7\u54c1\u7c7b\u578b"==t){for(var e=n.classify,s=[],i=0;i<e.length;i++)s.push({value:e[i]._id,_id:e[i]._id,label:e[i].flName});n.selectList=s}else"\u4e0a\u4e0b\u67b6"==t&&(n.selectList=[{value:0,b:!0,label:"\u4e0a\u67b6"},{value:1,b:!1,label:"\u4e0b\u67b6"}]);n.selectShow=!0,n.selectType=t},selectConfirmFun:function(t){"\u4ea7\u54c1\u7c7b\u578b"==n.selectType?(n.flName=t[0].label,n.cpFlId=t[0]._id||t[0].value):"\u4e0a\u4e0b\u67b6"==n.selectType&&(n.isSxJ=t[0].label)},uploadFmChangeFun:function(t){n.uploadType="fmt",n.ppList=t,n.ppUrls=n.cpFmt,n.ppImgShow=!0},fmtRemoeFun:function(t){var e=n.cpFmt.splice(t,1);n.deleteFun(e)},fileChangeFun:function(t){},uploadLbtChangeFun:function(t){n.uploadType="lbt",n.ppList=t,n.ppUrls=n.cpLbt,n.ppImgShow=!0},lbtRemoeFun:function(t){var e=n.cpLbt.splice(t,1);n.deleteFun(e)},uploadXqChangeFun:function(t){n.uploadType="xqt",n.ppList=t,n.ppUrls=n.cpXqt,n.ppImgShow=!0},xqtRemoeFun:function(t){var e=n.cpXqt.splice(t,1);n.deleteFun(e)},confirmPopupFun:function(t){n.ppImgShow=!1,"lbt"==n.uploadType?n.cpLbt=t:"xqt"==n.uploadType?n.cpXqt=t:n.cpFmt=t},deleteFun:function(t){var e={action:"delete",params:{fileList:[t[0].fileId]}};(0,r.fileApi)(e).then((function(t){}))},scanFun:function(){uni.scanCode({success:function(t){n.code=t.result}})},addXgFun:function(){var t={req:{pName:n.pName,price:n.price,unit:n.unit,cpFlId:n.cpFlId,isSxJ:"\u4e0a\u67b6"==n.isSxJ,code:n.code,describe:n.describe,cpCsList:n.cpCsList,cpLbtList:n.cpLbt,cpXqtList:n.cpXqt,cpFmtList:n.cpFmt,salesNum:n.salesNum,stock:n.stock}};if(n.pName)if(n.price)if(n.unit){var e="add",s=(new Date).getTime();"update"==n.pageType?(e="update",t._id=n.xgCpInfo._id,t.req.update_date=s,t.req.updateUid=uni.$userInfo._id,uni.showLoading({title:"\u4fee\u6539\u4e2d..."})):(t.req.create_date=s,t.req.update_date=s,t.req.cjRenId=uni.$userInfo._id,t.req.updateUid=uni.$userInfo._id,uni.showLoading({title:"\u63d0\u4ea4\u4e2d..."}));var i={action:e,params:t};(0,r.crmChanpinApi)(i).then((function(s){if("update"==e)uni.showToast({title:"\u4ea7\u54c1\u4fee\u6539\u6210\u529f",icon:"none",duration:1e3});else{n.pageType="update",n.xgCpInfo._id=s.result.id;var i=t.req;i._id=s.result.id,uni.$cpDetail=i,uni.showToast({mask:!0,title:"\u4ea7\u54c1\u65b0\u589e\u6210\u529f",duration:1e3}),setTimeout((function(){uni.redirectTo({url:"./cpDetail"})}),1e3)}}))}else uni.$myModalFun("\u4ea7\u54c1\u5355\u4f4d\u4e0d\u80fd\u4e3a\u7a7a\uff0c\u8bf7\u8f93\u5165\uff01");else uni.$myModalFun("\u4ea7\u54c1\u4ef7\u683c\u4e0d\u80fd\u4e3a\u7a7a\uff0c\u8bf7\u8f93\u5165\uff01");else uni.$myModalFun("\u4ea7\u54c1\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a\uff0c\u8bf7\u8f93\u5165\uff01")},addCsFun:function(){n.cpCsList=n.cpCsList.concat({csName:"",csJieShao:""})},deleteCpCsFun:function(t){n.cpCsList.splice(t,1)},setDataFun:function(){var t=uni.$cpDetail;n.xgCpInfo=t,n.pName=t.pName,n.price=t.price,n.unit=t.unit,n.flName=t.flName,n.cpFlId=t.cpFlId,n.isSxJ=t.isSxJ?"\u4e0a\u67b6":"\u4e0b\u67b6",n.code=t.code,n.describe=t.describe,n.cpCsList=t.cpCsList,n.cpLbtList=t.cpLbtList||[],n.cpXqtList=t.cpXqtList||[],n.cpFmtList=t.cpFmtList||[],n.cpLbt=t.cpLbtList||[],n.cpXqt=t.cpXqtList||[],n.cpFmt=t.cpFmtList||[],n.salesNum=t.salesNum||"",n.stock=t.stock||""}}};e.default=o},"5b43":function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return r})),s.d(e,"a",(function(){return i}));var i={topDropdown:s("aec3").default,dataNull:s("5e99").default,uImage:s("bbb4").default,uButton:s("2585").default,getMore:s("6e3d").default,addBtn:s("d66d").default},a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("view",[s("topDropdown",{attrs:{isSxShow:!0,dropdown1:t.dropdown1,dropdown2:"\u4e0a\u67b6\u4e2d",options1:t.options1,options2:t.isSjList,optionsSx:t.sxList,searchUrl:"./searchAdmin",_i:1},on:{dropDownFun1:t.dropDownFun1,dropDownFun2:t.dropDownFun2,optionSxFun:t.optionSxFun}}),t._$s(2,"i",0==t.list.length)?s("dataNull",{attrs:{src:"/static/img/chahua/dataNullXz.png",title:"\u6682\u65e0\u76f8\u5173\u4ea7\u54c1",title1:"\u8bf7\u6dfb\u52a0\u6216\u8005\u66f4\u6362\u67e5\u8be2\u6761\u4ef6",_i:2}}):s("scroll-view",{style:t._$s(3,"s",{height:t.scrollHeight}),attrs:{"refresher-triggered":t._$s(3,"a-refresher-triggered",t.triggered),_i:3},on:{scrolltolower:t.getChanPinFun,refresherrefresh:t.onRefresh,refresherrestore:t.onRestore}},[t._l(t._$s(4,"f",{forItems:t.list}),(function(e,i,a,r){return s("view",{key:t._$s(4,"f",{forIndex:a,key:i}),attrs:{_i:"4-"+r},on:{click:function(s){return t.cardClick(e)}}},[t._$s("5-"+r,"i",e.cpFmtList&&e.cpFmtList.length>0)?s("view",{staticClass:t._$s("5-"+r,"sc","myCard2"),attrs:{_i:"5-"+r}},[s("view",{staticClass:t._$s("6-"+r,"sc","leftImg"),attrs:{_i:"6-"+r}},[s("u-image",{attrs:{height:"160",width:"160","border-radius":"26",src:e.cpFmtList[0].url,_i:"7-"+r}})],1),s("view",{staticClass:t._$s("8-"+r,"sc","rightView"),attrs:{_i:"8-"+r}},[s("view",{staticClass:t._$s("9-"+r,"sc","cardTopName1"),attrs:{_i:"9-"+r}},[t._v(t._$s("9-"+r,"t0-0",t._s(e.pName)))]),t._$s("10-"+r,"i",e.salesNum||e.stock)?s("view",{staticClass:t._$s("10-"+r,"sc","xlKcClass"),attrs:{_i:"10-"+r}},[t._$s("11-"+r,"i",e.salesNum)?s("text",{attrs:{_i:"11-"+r}},[t._v(t._$s("11-"+r,"t0-0",t._s(e.salesNum)))]):t._e(),t._$s("12-"+r,"i",e.stock)?s("text",{attrs:{_i:"12-"+r}},[t._v(t._$s("12-"+r,"t0-0",t._s(e.stock)))]):t._e()]):t._e(),s("view",{staticClass:t._$s("13-"+r,"sc","cardRow1"),attrs:{_i:"13-"+r}},[s("text",{staticClass:t._$s("14-"+r,"sc","redColor"),attrs:{_i:"14-"+r}},[t._v(t._$s("14-"+r,"t0-0",t._s(e.price)))]),t._v(t._$s("13-"+r,"t1-0",t._s(e.unit)))]),s("view",{staticClass:t._$s("15-"+r,"sc","rowBtn"),attrs:{_i:"15-"+r}},[s("u-button",{staticClass:t._$s("16-"+r,"sc","cpBtn"),attrs:{type:"primary",plain:!0,size:"mini",_i:"16-"+r},on:{click:function(s){return s.stopPropagation(),t.setCpFun(e)}}},[t._v("")]),s("u-button",{staticClass:t._$s("17-"+r,"sc","cpBtn"),attrs:{type:"warning",plain:!0,size:"mini",_i:"17-"+r},on:{click:function(s){return t.cpsxjFun(e,i)}}},[t._v(t._$s("17-"+r,"t0-0",t._s(1==e.isSxJ?"\u4e0b\u67b6":"\u4e0a\u67b6")))]),s("u-button",{staticClass:t._$s("18-"+r,"sc","cpBtn"),attrs:{type:"error",plain:!0,size:"mini",_i:"18-"+r},on:{click:function(s){return s.stopPropagation(),t.deleteCpFun(e,i)}}},[t._v("")])],1)])]):s("view",{staticClass:t._$s("19-"+r,"sc","myCard"),attrs:{_i:"19-"+r}},[s("view",{staticClass:t._$s("20-"+r,"sc","cardTopName"),attrs:{_i:"20-"+r}},[t._v(t._$s("20-"+r,"t0-0",t._s(e.pName)))]),s("view",{staticClass:t._$s("21-"+r,"sc","cardRow"),attrs:{_i:"21-"+r}},[s("view"),s("view",[t._v(t._$s("23-"+r,"t0-0",t._s(e.isSxJ?"\u4e0a\u67b6\u4e2d":"\u4e0b\u67b6")))])]),s("view",{staticClass:t._$s("24-"+r,"sc","cardRow"),attrs:{_i:"24-"+r}},[s("view"),s("view",[s("text",{staticClass:t._$s("27-"+r,"sc","redColor"),attrs:{_i:"27-"+r}},[t._v(t._$s("27-"+r,"t0-0",t._s(e.price)))]),t._v(t._$s("26-"+r,"t1-0",t._s(e.unit)))])]),s("view",{staticClass:t._$s("28-"+r,"sc","cardRow"),attrs:{_i:"28-"+r}},[s("view"),s("view",[t._v(t._$s("30-"+r,"t0-0",t._s(e.flName)))])]),t._$s("31-"+r,"i",e.salesNum)?s("view",{staticClass:t._$s("31-"+r,"sc","cardRow"),attrs:{_i:"31-"+r}},[s("view"),s("view",[t._v(t._$s("33-"+r,"t0-0",t._s(e.salesNum)))])]):t._e(),t._$s("34-"+r,"i",e.stock)?s("view",{staticClass:t._$s("34-"+r,"sc","cardRow"),attrs:{_i:"34-"+r}},[s("view"),s("view",[t._v(t._$s("36-"+r,"t0-0",t._s(e.stock)))])]):t._e(),t._$s("37-"+r,"i",e.describe)?s("view",{staticClass:t._$s("37-"+r,"sc","cardRow"),attrs:{_i:"37-"+r}},[s("view"),s("view",[t._v(t._$s("39-"+r,"t0-0",t._s(e.describe)))])]):t._e(),s("view",{staticClass:t._$s("40-"+r,"sc","rowBtn"),attrs:{_i:"40-"+r}},[s("u-button",{staticClass:t._$s("41-"+r,"sc","cpBtn"),attrs:{type:"primary",plain:!0,size:"mini",_i:"41-"+r},on:{click:function(s){return s.stopPropagation(),t.setCpFun(e)}}},[t._v("")]),s("u-button",{staticClass:t._$s("42-"+r,"sc","cpBtn"),attrs:{type:"warning",plain:!0,size:"mini",_i:"42-"+r},on:{click:function(s){return t.cpsxjFun(e,i)}}},[t._v(t._$s("42-"+r,"t0-0",t._s(1==e.isSxJ?"\u4e0b\u67b6":"\u4e0a\u67b6")))]),s("u-button",{staticClass:t._$s("43-"+r,"sc","cpBtn"),attrs:{type:"error",plain:!0,size:"mini",_i:"43-"+r},on:{click:function(s){return s.stopPropagation(),t.deleteCpFun(e,i)}}},[t._v("")])],1)])])})),s("getMore",{staticClass:t._$s(44,"sc","h200"),attrs:{isMore:t.isMore,_i:44}})],2),s("addBtn",{attrs:{url:"./addCp",_i:45}})],1)},r=[]},"706d":function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return r})),s.d(e,"a",(function(){return i}));var i={uIcon:s("f7a4").default,skeleton:s("c943").default,uImage:s("bbb4").default,getMore:s("6e3d").default,dataNull:s("5e99").default,uPopup:s("7942").default},a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("view",[s("view",{staticClass:t._$s(1,"sc","top-box"),attrs:{_i:1}},[s("navigator",{staticClass:t._$s(2,"sc","top"),class:t._$s(2,"c",0==t.pageNum?"duanwidth":""),attrs:{_i:2}},[s("view",{staticClass:t._$s(3,"sc","search"),attrs:{_i:3}},[s("u-icon",{staticClass:t._$s(4,"sc","searchIcon"),attrs:{name:"search",_i:4}}),s("text")],1)])]),s("view",{staticClass:t._$s(6,"sc","scrollF"),style:t._$s(6,"s",{height:t.wHeight-50+"px"}),attrs:{_i:6}},[s("scroll-view",{staticClass:t._$s(7,"sc","leftScrollV"),attrs:{_i:7}},t._l(t._$s(8,"f",{forItems:t.tabBars}),(function(e,i,a,r){return s("view",{key:t._$s(8,"f",{forIndex:a,key:i}),staticClass:t._$s("8-"+r,"sc","leftCardView"),attrs:{_i:"8-"+r}},[t._$s("9-"+r,"i",e.isShow)?s("view",{staticClass:t._$s("9-"+r,"sc","leftCard"),class:t._$s("9-"+r,"c",{leftActive:t.leftA==i}),attrs:{_i:"9-"+r},on:{click:function(e){return t.selectLeftFl(i)}}},[t._v(t._$s("9-"+r,"t0-0",t._s(e.flName)))]):t._e()])})),0),s("view",{staticClass:t._$s(10,"sc","rightSv"),attrs:{_i:10}},[t._$s(11,"i",t.tabBars.length>0)?s("view",{attrs:{_i:11}},t._l(t._$s(12,"f",{forItems:t.tabBars}),(function(e,i,a,r){return s("scroll-view",{directives:[{name:"show",rawName:"v-show",value:t._$s("12-"+r,"v-show",t.leftA==i),expression:"_$s((\"12-\"+$31),'v-show',leftA == iii)"}],key:t._$s(12,"f",{forIndex:a,key:i}),attrs:{"refresher-triggered":t._$s("12-"+r,"a-refresher-triggered",e.triggered),_i:"12-"+r},on:{scrolltolower:t.loadMore,refresherrefresh:t.onRefresh,refresherrestore:t.onRestore}},[t._$s("13-"+r,"i",!e.isjz)?s("skeleton",{attrs:{SkelttionType:"classify",_i:"13-"+r}}):s("view",{attrs:{_i:"14-"+r}},[t._$s("15-"+r,"i",e.arr.length>0)?s("view",{attrs:{_i:"15-"+r}},[t._l(t._$s("16-"+r,"f",{forItems:e.arr}),(function(e,i,a,n){return s("view",{key:t._$s("16-"+r,"f",{forIndex:a,key:i}),attrs:{_i:"16-"+r+"-"+n},on:{click:function(s){return t.cardClick(e)}}},[t._$s("17-"+r+"-"+n,"i",e.cpFmtList&&e.cpFmtList.length>0)?s("view",{staticClass:t._$s("17-"+r+"-"+n,"sc","myCard2"),attrs:{_i:"17-"+r+"-"+n}},[s("view",{staticClass:t._$s("18-"+r+"-"+n,"sc","leftImg"),attrs:{_i:"18-"+r+"-"+n}},[s("u-image",{attrs:{height:"160",width:"160",src:e.cpFmtList[0].url,_i:"19-"+r+"-"+n}})],1),s("view",{staticClass:t._$s("20-"+r+"-"+n,"sc","rightView"),attrs:{_i:"20-"+r+"-"+n}},[s("view",{staticClass:t._$s("21-"+r+"-"+n,"sc","cardTopName1"),attrs:{_i:"21-"+r+"-"+n}},[t._v(t._$s("21-"+r+"-"+n,"t0-0",t._s(e.pName)))]),t._$s("22-"+r+"-"+n,"i",e.salesNum||e.stock)?s("view",{staticClass:t._$s("22-"+r+"-"+n,"sc","xlKcClass"),attrs:{_i:"22-"+r+"-"+n}},[t._$s("23-"+r+"-"+n,"i",e.salesNum)?s("text",{attrs:{_i:"23-"+r+"-"+n}},[t._v(t._$s("23-"+r+"-"+n,"t0-0",t._s(e.salesNum)))]):t._e(),t._$s("24-"+r+"-"+n,"i",e.stock)?s("text",{attrs:{_i:"24-"+r+"-"+n}},[t._v(t._$s("24-"+r+"-"+n,"t0-0",t._s(e.stock)))]):t._e()]):t._e(),s("view",{staticClass:t._$s("25-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"25-"+r+"-"+n}},[s("text",{staticClass:t._$s("26-"+r+"-"+n,"sc","redColor"),attrs:{_i:"26-"+r+"-"+n}},[t._v(t._$s("26-"+r+"-"+n,"t0-0",t._s(e.price)))]),t._v(t._$s("25-"+r+"-"+n,"t1-0",t._s(e.unit)))])])]):s("view",{staticClass:t._$s("27-"+r+"-"+n,"sc","myCard1"),attrs:{_i:"27-"+r+"-"+n}},[s("view",{staticClass:t._$s("28-"+r+"-"+n,"sc","cardTopName"),attrs:{_i:"28-"+r+"-"+n}},[t._v(t._$s("28-"+r+"-"+n,"t0-0",t._s(e.pName)))]),s("view",{staticClass:t._$s("29-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"29-"+r+"-"+n}},[s("view"),s("view",[s("text",{staticClass:t._$s("32-"+r+"-"+n,"sc","redColor"),attrs:{_i:"32-"+r+"-"+n}},[t._v(t._$s("32-"+r+"-"+n,"t0-0",t._s(e.price)))]),t._v(t._$s("31-"+r+"-"+n,"t1-0",t._s(e.unit)))])]),t._$s("33-"+r+"-"+n,"i",e.salesNum)?s("view",{staticClass:t._$s("33-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"33-"+r+"-"+n}},[s("view"),s("view",[t._v(t._$s("35-"+r+"-"+n,"t0-0",t._s(e.salesNum)))])]):t._e(),t._$s("36-"+r+"-"+n,"i",e.stock)?s("view",{staticClass:t._$s("36-"+r+"-"+n,"sc","cardRow1"),attrs:{_i:"36-"+r+"-"+n}},[s("view"),s("view",[t._v(t._$s("38-"+r+"-"+n,"t0-0",t._s(e.stock)))])]):t._e()])])})),s("getMore",{attrs:{isMore:t.tabBars[t.tabCurrentIndex].isMore,_i:"39-"+r}}),s("view")],2):s("dataNull",{attrs:{src:"/static/img/chahua/dataNullXz.png",title:"\u6682\u65e0\u76f8\u5173\u4ea7\u54c1",title1:"\u8bf7\u5148\u6dfb\u52a0",_i:"41-"+r}})],1)],1)})),0):s("dataNull",{attrs:{src:"/static/img/chahua/dataNullXz.png",title:"\u6682\u65e0\u76f8\u5173\u4ea7\u54c1",_i:42}})],1)]),s("u-popup",{attrs:{width:"568rpx",mode:"right","border-radius":"14","safe-area-inset-bottom":!0,_i:43},model:{value:t._$s(43,"v-model",t.popupShow),callback:function(e){t.popupShow=e},expression:"popupShow"}},[t._l(t._$s(44,"f",{forItems:t.sxArr}),(function(e,i,a,r){return s("view",{key:t._$s(44,"f",{forIndex:a,key:i}),staticClass:t._$s("44-"+r,"sc","popupCard"),attrs:{_i:"44-"+r}},[s("view",{staticClass:t._$s("45-"+r,"sc","popupTitle"),attrs:{_i:"45-"+r}},[t._v(t._$s("45-"+r,"t0-0",t._s(e.title)))]),s("view",{staticClass:t._$s("46-"+r,"sc","popupItem"),attrs:{_i:"46-"+r}},t._l(t._$s("47-"+r,"f",{forItems:e.arr}),(function(a,n,o,c){return s("view",{key:t._$s("47-"+r,"f",{forIndex:o,key:n}),class:t._$s("47-"+r+"-"+c,"c",e.current===n?"activeClass":""),attrs:{_i:"47-"+r+"-"+c},on:{click:function(e){return t.sxItemClickFun(i,n)}}},[t._v(t._$s("47-"+r+"-"+c,"t0-0",t._s(a)))])})),0),t._$s("48-"+r,"i",e.judge)?s("view",{staticClass:t._$s("48-"+r,"sc","flexJs"),attrs:{_i:"48-"+r}},[s("text"),s("u-icon",{attrs:{name:"arrow-right",_i:"50-"+r}})],1):t._e()])})),s("view",{staticClass:t._$s(51,"sc","h200"),attrs:{_i:51}}),s("view",{staticClass:t._$s(52,"sc","popupBottomBtn"),attrs:{_i:52}},[s("view",{staticClass:t._$s(53,"sc","popupBtn warning"),attrs:{_i:53},on:{click:t.resetFun}}),s("view",{staticClass:t._$s(54,"sc","popupBtn primary"),attrs:{_i:54},on:{click:t.confirmFun}})])],2)],1)},r=[]},7804:function(t,e,s){"use strict";s.r(e);var i=s("b3f8"),a=s.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"81fe":function(t,e,s){"use strict";s.r(e);var i=s("706d"),a=s("9431");for(var r in a)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(r);var n=s("828b"),o=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=o.exports},8895:function(t,e,s){"use strict";(function(t){var i=s("47a9"),a=s("3b2d");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(s("5e99")),n=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!==a(t)&&"function"!==typeof t)return{default:t};var s=o(e);if(s&&s.has(t))return s.get(t);var i={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in t)if("default"!==n&&Object.prototype.hasOwnProperty.call(t,n)){var c=r?Object.getOwnPropertyDescriptor(t,n):null;c&&(c.get||c.set)?Object.defineProperty(i,n,c):i[n]=t[n]}i.default=t,s&&s.set(t,i);return i}(s("8b24"));function o(t){if("function"!==typeof WeakMap)return null;var e=new WeakMap,s=new WeakMap;return(o=function(t){return t?s:e})(t)}var c="",l={components:{dataNull:r.default},data:function(){return{triggered:!1,scrollHeight:"667px",customerID:0,GoodsCodeNo:"",GoodsCodeName:"",tabBars:[],list:[],detail:{},background:{"background-image":"linear-gradient(45deg, #007aff, rgb(12, 168, 234))"},customStyle:{backgroundColor:"rgb(13, 159, 224)",color:"#FFFFFF",border:"0",fontSize:"32",outline:"none"},lbt:[],pageType:""}},onLoad:function(e){c=this,c.detail=uni.$cpDetail,t("log","------"+c.detail.FabricGoodsName," at chanpin/view/cpDetail.vue:93"),this.customerID=e.customerid,this.item=JSON.parse(decodeURIComponent(e.obj)),this.getStoreGoodsCodeData(e)},methods:{getStoreGoodsCodeData:function(e){var s=this;c.leftA;t("log","this.item.StoreNameID==="+this.item.StoreNameID," at chanpin/view/cpDetail.vue:102"),t("log","this.item.OwnerID==="+this.item.OwnerID," at chanpin/view/cpDetail.vue:103"),t("log","this.item.CustomerID==="+this.customerID," at chanpin/view/cpDetail.vue:104"),t("log","this.item.FabricGoodsID==="+this.item.FabricGoodsID," at chanpin/view/cpDetail.vue:105"),t("log","this.item.GoodsGradeName==="+this.item.GoodsGradeName," at chanpin/view/cpDetail.vue:106"),t("log","this.item.GoodsRemark==="+this.item.GoodsRemark," at chanpin/view/cpDetail.vue:107"),uni.request({url:n.default.apiurl+"rest/db/opensql",data:{token:getApp().globalData.Token,format:"json",data:{db_name:getApp().globalData.AppDBName,sql_command_id:"APP.StoreGoods.GetStoreGoodsMasterGoodsCode",params:[{name:"SID",value:this.item.StoreNameID},{name:"OID",value:this.item.OwnerID},{name:"CID",value:this.customerID},{name:"FGID",value:this.item.FabricGoodsID},{name:"GradeName",value:this.item.GoodsGradeName},{name:"GoodsRemark",value:this.item.GoodsRemark},{name:"GCNo",value:"%"+this.GoodsCodeNo+"%"},{name:"GCName",value:"%"+this.GoodsCodeName+"%"}]}},success:function(e){s.triggered=!1;var i=e.data.data;1==c.pageIndex&&(c.list=[]),20==e.data?(c.pageIndex+=1,c.isMore=!0):c.isMore=!1,c.list=c.list.concat(i),t("log","-----\x3e>"+JSON.stringify(c.list)," at chanpin/view/cpDetail.vue:156")}})},StoreGoodsCodeCardClickFun:function(t){uni.$emit("bjdKehuBindFun",{CustomerID:t.CustomerID,CustomerName:t.CustomerName,PlanDepartmentID:t.PlanDepartmentID,PlanDepartmentName:t.PlanDepartmentName,SaleUserID:t.SaleUserID,SaleUserName:t.SaleUserName,Remark:t.Remark,CustomerAddress:t.CustomerAddress,CustomerPhone:t.CustomerPhone,CustomerLinkName:t.CustomerLinkName}),uni.navigateBack()}}};e.default=l}).call(this,s("f3b9")["default"])},"8dad":function(t,e,s){"use strict";var i=s("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(s("5e99")),r=i(s("d66d")),n=i(s("6e3d")),o=i(s("ad50")),c=s("eae8"),l="",u={components:{searchView:o.default,dataNull:a.default,getMore:n.default,addBtn:r.default},data:function(){return{pageIndex:1,searchValue:"",list:[],scrollHeight:"667px",triggered:!1,isMore:!0,sortObj:{update_date:-1}}},onLoad:function(t){l=this,uni.getSystemInfo({success:function(t){l.scrollHeight=t.windowHeight-40+"px"}})},methods:{getChanPinFun:function(){if(l.isMore){uni.showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0});var t={matchObj:{isSxJ:!0},pageIndex:l.pageIndex,searchValue:l.searchValue,sortObj:l.sortObj},e={action:"getCpList",params:t};(0,c.crmChanpinApi)(e).then((function(t){var e=t.result.data;1==l.pageIndex&&(l.list=[]),20==e.length?(l.pageIndex+=1,l.isMore=!0):l.isMore=!1,l.triggered=!1,l.list=l.list.concat(e)}))}},cardClick:function(t){uni.$cpDetail=t,uni.navigateTo({url:"./cpDetail?type=client"})},searchEventsFun:function(t){l.searchValue=t,l.isMore=!0,l.pageIndex=1,l.getChanPinFun()},onRefresh:function(){l.triggered||(l.triggered=!0,l.pageIndex=1,l.isMore=!0,l.getChanPinFun())},onRestore:function(t){l.triggered=!1}}};e.default=u},"8e10":function(t,e,s){"use strict";s.r(e);var i=s("3c2c"),a=s("d1f2");for(var r in a)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(r);var n=s("828b"),o=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,"9550fb62",null,!1,i["a"],void 0);e["default"]=o.exports},"8f65":function(t,e,s){"use strict";(function(t){var i=s("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(s("5e99")),r=i(s("d66d")),n=i(s("6e3d")),o=i(s("aec3")),c=s("eae8"),l="",u={components:{dataNull:a.default,addBtn:r.default,getMore:n.default,topDropdown:o.default},data:function(){return{triggered:!1,pageIndex:1,list:[],classify:[],dropdown1:"\u4ea7\u54c1\u5206\u7c7b",cpClassify:"",cpFlId:"",isMore:!0,scrollHeight:"667px",sjType:"\u4e0a\u67b6\u4e2d",options1:[],isSjList:[{value:0,label:"\u4e0a\u67b6\u4e2d"},{value:1,label:"\u4e0b\u67b6"}],isSxJ:!0,sxList:[{title:"\u521b\u5efa\u65f6\u95f4\u6392\u5e8f",field:"create_date",current:"",arr:["\u964d\u5e8f","\u5347\u5e8f"]},{title:"\u4fee\u6539\u65f6\u95f4\u6392\u5e8f",field:"update_date",current:"",arr:["\u964d\u5e8f","\u5347\u5e8f"]},{title:"\u9500\u91cf\u6392\u5e8f",field:"salesNum",current:"",arr:["\u964d\u5e8f","\u5347\u5e8f"]},{title:"\u5e93\u5b58\u6392\u5e8f",field:"stock",current:"",arr:["\u964d\u5e8f","\u5347\u5e8f"]}],sortObj:{update_date:-1}}},onLoad:function(){l=this,uni.getSystemInfo({success:function(t){l.scrollHeight=t.windowHeight-40+"px"}}),l.getClassifyFun()},methods:{getClassifyFun:function(){uni.showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0});(0,c.crmCpClassifyApi)({action:"get",params:{isShow:!0}}).then((function(t){var e=t.result.data;e&&e.length>0?(l.classify=e,l.cpClassify=e[0].flName,l.cpFlId=e[0]._id,l.setDropDownFun(),l.getChanPinFun()):uni.showModal({title:"\u63d0\u793a",content:"\u5f53\u524d\u6682\u672a\u8bbe\u7f6e\u4ea7\u54c1\u5206\u7c7b\uff0c\u8bf7\u5148\u6dfb\u52a0\u4ea7\u54c1\u5206\u7c7b\uff01",success:function(t){t.confirm&&uni.redirectTo({url:"./classify?pageType=add"})}})}))},getChanPinFun:function(){if(l.isMore){uni.showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0});var e={matchObj:{cpFlId:l.cpFlId,isSxJ:l.isSxJ},pageIndex:l.pageIndex,sortObj:l.sortObj},s={action:"getCpList",params:e};t("log",s," at chanpin/view/chanpin.vue:225"),(0,c.crmChanpinApi)(s).then((function(t){var e=t.result.data;1==l.pageIndex&&(l.list=[]),20==e.length?(l.pageIndex+=1,l.isMore=!0):l.isMore=!1,l.triggered=!1,l.list=l.list.concat(e)}))}},setDropDownFun:function(){for(var t=l.classify,e=[],s=0;s<t.length;s++)e.push({value:s,_id:t[s]._id,label:t[s].flName});l.options1=e,l.dropdown1=e[0].label},dropDownFun1:function(t){l.cpClassify=t.label,l.cpFlId=t._id,l.pageIndex=1,l.isMore=!0,l.getChanPinFun()},dropDownFun2:function(t){"\u4e0a\u67b6\u4e2d"==t.label?l.isSxJ=!0:l.isSxJ=!1,l.pageIndex=1,l.isMore=!0,l.getChanPinFun()},optionSxFun:function(t){for(var e={update_date:-1},s=0;s<t.length;s++)if(""!==t[s].current){"create_date"==t[s].field&&(e.create_date=1==t[s].current?1:-1),"update_date"==t[s].field&&(e.update_date=1==t[s].current?1:-1),"salesNum"==t[s].field&&(e.salesNum=1==t[s].current?1:-1),"stock"==t[s].field&&(e.stock=1==t[s].current?1:-1),l.sortObj=e;break}l.pageIndex=1,l.isMore=!0,l.getChanPinFun()},cardClick:function(t){uni.$cpDetail=t,uni.navigateTo({url:"./cpDetail"})},setCpFun:function(t){uni.$cpDetail=t,uni.navigateTo({url:"./addCp?type=update"})},deleteCpFun:function(t,e){uni.showModal({title:"\u63d0\u793a",content:"\u662f\u5426\u786e\u8ba4\u5220\u9664\u4ea7\u54c1\uff1a"+t.pName,success:function(s){if(s.confirm){uni.showLoading({title:"\u5220\u9664\u4e2d..."});var i=t.cpFmtList;i=i.concat(t.cpLbtList),i=i.concat(t.cpXqtList),i.length>0?l.deleteCpFileFun(t._id,e,i):l.deleteCpApiFun(t._id,e)}}})},deleteCpApiFun:function(t,e){var s={action:"deleteById",params:{_id:t}};(0,c.crmChanpinApi)(s).then((function(t){uni.showToast({title:"\u5220\u9664\u6210\u529f!",icon:"none",duration:1e3}),l.list.splice(e,1)}))},deleteCpFileFun:function(t,e,s){for(var i=[],a=0;a<s.length;a++)i.push(s[a].fileId);var r={action:"delete",params:{fileList:i}};(0,c.fileApi)(r).then((function(s){l.deleteCpApiFun(t,e)}))},cpsxjFun:function(t,e){var s="\u4e0a\u67b6\u4e2d...",i=!0;t.isSxJ&&(i=!1,s="\u4e0b\u67b6\u4e2d..."),uni.showLoading({title:s,mask:!0});var a={_id:t._id,req:{isSxJ:i}},r={action:"update",params:a};(0,c.crmChanpinApi)(r).then((function(t){var s="\u4e0a\u67b6\u6210\u529f";0==i&&(s="\u4e0b\u67b6\u6210\u529f\uff01"),uni.showToast({title:s,icon:"none",duration:2e3}),l.list.splice(e,1)}))},onRefresh:function(){l.triggered||(l.triggered=!0,l.pageIndex=1,l.isMore=!0,l.getChanPinFun())},onRestore:function(t){l.triggered=!1}}};e.default=u}).call(this,s("f3b9")["default"])},9431:function(t,e,s){"use strict";s.r(e);var i=s("1839"),a=s.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},ab1f:function(t,e,s){"use strict";s.r(e);var i=s("edb0"),a=s("e8ee");for(var r in a)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(r);var n=s("828b"),o=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=o.exports},b0fa:function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return r})),s.d(e,"a",(function(){return i}));var i={uNavbar:s("f9b5").default,getMore:s("6e3d").default},a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("view",[s("u-navbar",{attrs:{"back-icon-color":"#FFFFFF",title:"\u6210\u54c1\u5e93\u5b58\u8d44\u6599","title-color":"#FFFFFF","title-bold":!0,background:t.background,_i:1}}),s("view",{staticClass:t._$s(2,"sc","top-box"),attrs:{_i:2}},[s("view",{staticClass:t._$s(3,"sc","myCard"),attrs:{_i:3}},[s("view",{staticClass:t._$s(4,"sc","cardTopName"),attrs:{_i:4}},[t._v(t._$s(4,"t0-0",t._s(this.item.FabricGoodsNo))+t._$s(4,"t0-1",t._s(this.item.FabricGoodsName)))]),t._$s(5,"i",this.item.GoodsWeight)?s("view",{staticClass:t._$s(5,"sc","cardRow1"),attrs:{_i:5}},[s("view",[t._v(t._$s(6,"t0-0",t._s(this.item.GoodsWidth))+t._$s(6,"t0-1",t._s(this.item.GoodsWeight)))])]):t._e(),s("view",{staticClass:t._$s(7,"sc","cardRow1"),attrs:{_i:7}},[s("view",[t._v(t._$s(8,"t0-0",t._s(this.item.StoreName))+t._$s(8,"t0-1",t._s(this.item.OwnerName)))])]),s("view",{staticClass:t._$s(9,"sc","cardRow1"),attrs:{_i:9}},[s("view",[t._v(t._$s(10,"t0-0",t._s(this.item.GoodsGradeName)))])]),s("view",{staticClass:t._$s(11,"sc","cardRow1"),attrs:{_i:11}},[s("view",[t._v(t._$s(12,"t0-0",t._s(this.item.GoodsRemark)))])])])]),s("scroll-view",{style:t._$s(13,"s",{height:t.scrollHeight}),attrs:{"refresher-triggered":t._$s(13,"a-refresher-triggered",t.triggered),_i:13},on:{refresherrefresh:t.onRefresh,refresherrestore:t.onRestore}},[t._$s(14,"i",t.list.length>0)?s("view",{attrs:{_i:14}},[t._l(t._$s(15,"f",{forItems:t.list}),(function(e,i,a,r){return s("view",{key:t._$s(15,"f",{forIndex:a,key:i}),attrs:{_i:"15-"+r},on:{click:function(s){return t.StoreGoodsCodeCardClickFun(e)}}},[s("view",{staticClass:t._$s("16-"+r,"sc","myCard"),attrs:{_i:"16-"+r}},[s("view",{staticClass:t._$s("17-"+r,"sc","cardRow1"),attrs:{_i:"17-"+r}},[s("view",[t._v(t._$s("18-"+r,"t0-0",t._s(e.GoodsCodeNo))+t._$s("18-"+r,"t0-1",t._s(e.GoodsCodeName)))])]),s("view",{staticClass:t._$s("19-"+r,"sc","cardRow1"),attrs:{_i:"19-"+r}},[s("view",{staticClass:t._$s("20-"+r,"sc","mr26"),attrs:{_i:"20-"+r}},[t._v(t._$s("20-"+r,"t0-0",t._s(e.Roll)))]),s("view",{staticClass:t._$s("21-"+r,"sc","mr26"),attrs:{_i:"21-"+r}},[t._v(t._$s("21-"+r,"t0-0",t._s(e.Qty))+t._$s("21-"+r,"t0-1",t._s(e.UnitName)))])]),s("view",{staticClass:t._$s("22-"+r,"sc","cardRow1"),attrs:{_i:"22-"+r}},[s("view",{staticClass:t._$s("23-"+r,"sc","mr26"),attrs:{_i:"23-"+r}},[t._v(t._$s("23-"+r,"t0-0",t._s(e.AllocatedRoll)))]),s("view",{staticClass:t._$s("24-"+r,"sc","mr26"),attrs:{_i:"24-"+r}},[t._v(t._$s("24-"+r,"t0-0",t._s(e.UseRoll)))])]),t._$s("25-"+r,"i",e.Price>0)?s("view",{staticClass:t._$s("25-"+r,"sc","cardRow1"),attrs:{_i:"25-"+r}},[s("view",{staticClass:t._$s("26-"+r,"sc","mr26"),attrs:{_i:"26-"+r}},[t._v(t._$s("26-"+r,"t0-0",t._s(e.Price))+t._$s("26-"+r,"t0-1",t._s(e.UnitName)))])]):t._e()])])})),s("getMore",{attrs:{isMore:t.isMore,nullMsg:"\u5df2\u52a0\u8f7d\u5168\u90e8~",_i:27}}),s("view",{staticClass:t._$s(28,"sc","h200"),attrs:{_i:28}})],2):t._e()])],1)},r=[]},b3f8:function(t,e,s){"use strict";var i=s("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(s("5e99")),r=i(s("d66d")),n=i(s("6e3d")),o=i(s("ad50")),c=s("eae8"),l="",u={components:{searchView:o.default,dataNull:a.default,getMore:n.default,addBtn:r.default},data:function(){return{pageIndex:1,searchValue:"",list:[],scrollHeight:"667px",triggered:!1,isMore:!0,sortObj:{update_date:-1}}},onLoad:function(){l=this,uni.getSystemInfo({success:function(t){l.scrollHeight=t.windowHeight-40+"px"}})},methods:{getChanPinFun:function(){if(l.isMore){uni.showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0});var t={pageIndex:l.pageIndex,searchValue:l.searchValue,sortObj:l.sortObj},e={action:"get",params:t};(0,c.crmChanpinApi)(e).then((function(t){var e=t.result.data;1==l.pageIndex&&(l.list=[]),10==e.length?(l.pageIndex+=1,l.isMore=!0):l.isMore=!1,l.triggered=!1,l.list=l.list.concat(e)}))}},cardClick:function(t){uni.$cpDetail=t,uni.navigateTo({url:"./cpDetail"})},searchEventsFun:function(t){l.searchValue=t,l.isMore=!0,l.pageIndex=1,l.getChanPinFun()},onRefresh:function(){l.triggered||(l.triggered=!0,l.pageIndex=1,l.isMore=!0,l.getChanPinFun())},onRestore:function(t){l.triggered=!1}}};e.default=u},d1f2:function(t,e,s){"use strict";s.r(e);var i=s("e971"),a=s.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},e78d:function(t,e,s){"use strict";s.r(e);var i=s("565a"),a=s.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},e8ee:function(t,e,s){"use strict";s.r(e);var i=s("8dad"),a=s.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},e971:function(t,e,s){"use strict";(function(t){var i=s("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(s("5e99")),r=i(s("d66d")),n=s("eae8"),o="",c={components:{dataNull:a.default,addBtn:r.default},data:function(){return{popupShow:!1,popupXgShow:!1,addName:"",flName:"",sort:"",flData:{},list:[],xgObj:{},ispush:!1,pageType:""}},onLoad:function(t){o=this,t.pageType&&(o.pageType=t.pageType,o.popupShow=!0),o.getClassifyFun()},methods:{getClassifyFun:function(){uni.showLoading({title:"\u52a0\u8f7d\u4e2d...",mask:!0});(0,n.crmCpClassifyApi)({action:"getFlList",params:{}}).then((function(e){var s=e.result.data;t("log",s," at chanpin/view/classify.vue:102"),o.list=s}))},addClassifyFun:function(){if(o.flName)if(o.sort){uni.showLoading({title:"\u63d0\u4ea4\u4e2d...",mask:!0});var t={flName:o.flName,sort:parseFloat(o.sort),isShow:!0},e=(new Date).getTime();t.create_date=e,t.update_date=e,t.cjRenId=uni.$userInfo._id,t.updateUid=uni.$userInfo._id;var s={action:"add",params:{req:t}};(0,n.crmCpClassifyApi)(s).then((function(t){o.flName="",o.sort="",o.popupShow=!1,uni.showToast({title:"\u65b0\u589e\u6210\u529f\uff01",icon:"none",duration:2e3}),o.getClassifyFun()}))}else uni.showToast({title:"\u8bf7\u8f93\u5165\u5206\u7c7b\u6392\u5e8f\u6570\u503c\uff01",icon:"none"});else uni.showToast({title:"\u8bf7\u8f93\u5165\u5206\u7c7b\u540d\u79f0\uff01",icon:"none"})},updateNameFun:function(t){o.popupXgShow=!0,o.xgObj=t,o.sort=t.sort,o.flName=t.flName},updateConfirmFun:function(){if(o.flName)if(o.sort){uni.showLoading({title:"\u66f4\u65b0\u4e2d...",mask:!0});var t={flName:o.flName,sort:parseFloat(o.sort)},e=(new Date).getTime();t.update_date=e,t.updateUid=uni.$userInfo._id;var s={action:"updateFl",params:{_id:o.xgObj._id,updateObj:t}};(0,n.crmCpClassifyApi)(s).then((function(t){o.flName="",o.sort="",o.popupXgShow=!1,uni.showToast({title:"\u4fee\u6539\u6210\u529f\uff01",icon:"none",duration:2e3}),o.getClassifyFun()}))}else uni.showToast({title:"\u8bf7\u8f93\u5165\u5206\u7c7b\u6392\u5e8f\u6570\u503c\uff01",icon:"none"});else uni.showToast({title:"\u8bf7\u8f93\u5165\u5206\u7c7b\u540d\u79f0\uff01",icon:"none"})},changeClassFun:function(t){var e={action:"updateFl",params:{_id:t._id,updateObj:{isShow:t.isShow}}};(0,n.crmCpClassifyApi)(e).then((function(t){o.getClassifyFun()}))},deleteFun:function(t){uni.showModal({title:"\u63d0\u793a",content:"\u662f\u5426\u786e\u8ba4\u8981\u5220\u9664\u8be5\u4ea7\u54c1\u5206\u7c7b\uff1a"+t.flName+"?",success:function(e){if(e.confirm){uni.showLoading({title:"\u5220\u9664\u4e2d\uff0c\u8bf7\u7a0d\u7b49...",mask:!0});var s={action:"deleteFlById",params:{_id:t._id}};(0,n.crmCpClassifyApi)(s).then((function(t){o.getClassifyFun()}))}}})}}};e.default=c}).call(this,s("f3b9")["default"])},e9cc:function(t,e,s){"use strict";s.r(e);var i=s("b0fa"),a=s("3673");for(var r in a)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(r);var n=s("828b"),o=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=o.exports},edb0:function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return r})),s.d(e,"a",(function(){return i}));var i={searchView:s("ad50").default,dataNull:s("5e99").default,uImage:s("bbb4").default,getMore:s("6e3d").default},a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("view",[s("searchView",{attrs:{placeholderStr:"\u8bf7\u8f93\u5165\u4ea7\u54c1\u540d\u79f0",_i:1},on:{searchViewClickFun:function(e){return t.searchEventsFun(e)}}}),t._$s(2,"i",0==t.list.length)?s("dataNull",{attrs:{src:"/static/img/chahua/dataNullXz.png",title:"\u6682\u65e0\u76f8\u5173\u4ea7\u54c1",title1:"\u8bf7\u66f4\u6362\u67e5\u8be2\u6761\u4ef6",_i:2}}):s("scroll-view",{style:t._$s(3,"s",{height:t.scrollHeight}),attrs:{"refresher-triggered":t._$s(3,"a-refresher-triggered",t.triggered),_i:3},on:{scrolltolower:t.getChanPinFun,refresherrefresh:t.onRefresh,refresherrestore:t.onRestore}},[t._l(t._$s(4,"f",{forItems:t.list}),(function(e,i,a,r){return s("view",{key:t._$s(4,"f",{forIndex:a,key:i}),attrs:{_i:"4-"+r},on:{click:function(s){return t.cardClick(e)}}},[t._$s("5-"+r,"i",e.cpFmtList&&e.cpFmtList.length>0)?s("view",{staticClass:t._$s("5-"+r,"sc","myCard2"),attrs:{_i:"5-"+r}},[s("view",{staticClass:t._$s("6-"+r,"sc","leftImg"),attrs:{_i:"6-"+r}},[s("u-image",{attrs:{height:"160",width:"160","border-radius":"26",src:e.cpFmtList[0].url,_i:"7-"+r}})],1),s("view",{staticClass:t._$s("8-"+r,"sc","rightView"),attrs:{_i:"8-"+r}},[s("view",{staticClass:t._$s("9-"+r,"sc","cardTopName1"),attrs:{_i:"9-"+r}},[t._v(t._$s("9-"+r,"t0-0",t._s(e.pName)))]),t._$s("10-"+r,"i",e.salesNum||e.stock)?s("view",{staticClass:t._$s("10-"+r,"sc","xlKcClass"),attrs:{_i:"10-"+r}},[t._$s("11-"+r,"i",e.salesNum)?s("text",{attrs:{_i:"11-"+r}},[t._v(t._$s("11-"+r,"t0-0",t._s(e.salesNum)))]):t._e(),t._$s("12-"+r,"i",e.stock)?s("text",{attrs:{_i:"12-"+r}},[t._v(t._$s("12-"+r,"t0-0",t._s(e.stock)))]):t._e()]):t._e(),s("view",{staticClass:t._$s("13-"+r,"sc","cardRow1"),attrs:{_i:"13-"+r}},[s("text",{staticClass:t._$s("14-"+r,"sc","redColor"),attrs:{_i:"14-"+r}},[t._v(t._$s("14-"+r,"t0-0",t._s(e.price)))]),t._v(t._$s("13-"+r,"t1-0",t._s(e.unit)))])])]):s("view",{staticClass:t._$s("15-"+r,"sc","myCard"),attrs:{_i:"15-"+r}},[s("view",{staticClass:t._$s("16-"+r,"sc","cardTopName"),attrs:{_i:"16-"+r}},[t._v(t._$s("16-"+r,"t0-0",t._s(e.pName)))]),s("view",{staticClass:t._$s("17-"+r,"sc","cardRow"),attrs:{_i:"17-"+r}},[s("view"),s("view",[t._v(t._$s("19-"+r,"t0-0",t._s(e.isSxJ)))])]),s("view",{staticClass:t._$s("20-"+r,"sc","cardRow"),attrs:{_i:"20-"+r}},[s("view"),s("view",[s("text",{staticClass:t._$s("23-"+r,"sc","redColor"),attrs:{_i:"23-"+r}},[t._v(t._$s("23-"+r,"t0-0",t._s(e.price)))]),t._v(t._$s("22-"+r,"t1-0",t._s(e.unit)))])]),s("view",{staticClass:t._$s("24-"+r,"sc","cardRow"),attrs:{_i:"24-"+r}},[s("view"),s("view",[t._v(t._$s("26-"+r,"t0-0",t._s(e.cpClassify)))])]),t._$s("27-"+r,"i",e.salesNum)?s("view",{staticClass:t._$s("27-"+r,"sc","cardRow"),attrs:{_i:"27-"+r}},[s("view"),s("view",[t._v(t._$s("29-"+r,"t0-0",t._s(e.salesNum)))])]):t._e(),t._$s("30-"+r,"i",e.stock)?s("view",{staticClass:t._$s("30-"+r,"sc","cardRow"),attrs:{_i:"30-"+r}},[s("view"),s("view",[t._v(t._$s("32-"+r,"t0-0",t._s(e.stock)))])]):t._e(),t._$s("33-"+r,"i",e.describe)?s("view",{staticClass:t._$s("33-"+r,"sc","cardRow"),attrs:{_i:"33-"+r}},[s("view"),s("view",[t._v(t._$s("35-"+r,"t0-0",t._s(e.describe)))])]):t._e()])])})),s("getMore",{staticClass:t._$s(36,"sc","h100"),attrs:{isMore:t.isMore,_i:36}})],2)],1)},r=[]},ef62:function(t,e,s){"use strict";s.r(e);var i=s("8f65"),a=s.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},fd95:function(t,e,s){"use strict";s.r(e);var i=s("299a"),a=s("e78d");for(var r in a)["default"].indexOf(r)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(r);var n=s("828b"),o=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=o.exports}}]);