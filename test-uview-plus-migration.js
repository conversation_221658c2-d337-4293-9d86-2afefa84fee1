/**
 * uView Plus 迁移测试脚本
 * 验证从 uview-ui 到 uview-plus 的迁移是否成功
 */

const fs = require('fs');
const path = require('path');

function testUViewPlusMigration() {
  console.log('🚀 开始 uView Plus 迁移测试...\n');
  
  const tests = [
    {
      name: 'uview-plus 包安装检查',
      test: () => {
        try {
          const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
          return packageJson.dependencies && packageJson.dependencies['uview-plus'];
        } catch (e) {
          console.error('读取 package.json 失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'uview-plus 模块导入测试',
      test: () => {
        try {
          require('uview-plus');
          return true;
        } catch (e) {
          console.error('uview-plus 导入失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'main.js 更新检查',
      test: () => {
        try {
          const mainJs = fs.readFileSync('main.js', 'utf8');
          return mainJs.includes('uview-plus') && !mainJs.includes('uview-ui');
        } catch (e) {
          console.error('读取 main.js 失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'App.vue 样式更新检查',
      test: () => {
        try {
          const appVue = fs.readFileSync('App.vue', 'utf8');
          return appVue.includes('uview-plus/index.scss') && !appVue.includes('uview-ui/index.scss');
        } catch (e) {
          console.error('读取 App.vue 失败:', e.message);
          return false;
        }
      }
    },
    {
      name: '旧 uview-ui 文件夹清理检查',
      test: () => {
        return !fs.existsSync('uview-ui');
      }
    },
    {
      name: 'uview-plus mixin 文件存在检查',
      test: () => {
        try {
          const mpSharePath = 'node_modules/.pnpm/uview-plus@3.4.57/node_modules/uview-plus/libs/mixin/mpShare.js';
          return fs.existsSync(mpSharePath);
        } catch (e) {
          console.error('检查 mixin 文件失败:', e.message);
          return false;
        }
      }
    },
    {
      name: 'uview-plus 组件数量检查',
      test: () => {
        try {
          const componentsPath = 'node_modules/.pnpm/uview-plus@3.4.57/node_modules/uview-plus/components';
          if (!fs.existsSync(componentsPath)) return false;
          
          const components = fs.readdirSync(componentsPath);
          // uview-plus 应该有大量组件 (>50)
          return components.length > 50;
        } catch (e) {
          console.error('检查组件数量失败:', e.message);
          return false;
        }
      }
    }
  ];
  
  let passedTests = 0;
  const totalTests = tests.length;
  
  tests.forEach((test, index) => {
    process.stdout.write(`${index + 1}. ${test.name}... `);
    
    try {
      const result = test.test();
      if (result) {
        console.log('✅ 通过');
        passedTests++;
      } else {
        console.log('❌ 失败');
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
    }
  });
  
  console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 uView Plus 迁移成功！');
    console.log('\n✨ 迁移完成的改进:');
    console.log('• ✅ 完全支持 Vue 3');
    console.log('• ✅ 更好的 TypeScript 支持');
    console.log('• ✅ 更现代的组件 API');
    console.log('• ✅ 更好的性能表现');
    console.log('• ✅ 持续的维护和更新');
  } else {
    console.log('⚠️  部分测试失败，请检查相关配置。');
  }
  
  // 输出下一步建议
  console.log('\n📋 下一步建议:');
  console.log('1. 在 HBuilderX 中启动项目测试');
  console.log('2. 检查所有使用 u- 前缀组件的页面');
  console.log('3. 测试 storefabricBusinessOutAdd.vue 页面');
  console.log('4. 验证所有 uView 组件功能正常');
  console.log('5. 检查样式是否正确加载');
  
  // 输出可能需要手动检查的组件
  console.log('\n🔍 需要手动验证的组件:');
  console.log('• u-form, u-form-item');
  console.log('• u-input, u-textarea');
  console.log('• u-button');
  console.log('• u-select');
  console.log('• u-toast');
  console.log('• u-icon');
  
  return passedTests === totalTests;
}

// 运行测试
if (require.main === module) {
  testUViewPlusMigration();
}

module.exports = { testUViewPlusMigration };
