{"name": "浩拓技术1", "appid": "__UNI__F79F300", "description": "浩拓纺织平台", "versionName": "1.8.5", "versionCode": "100", "transformPx": false, "app-plus": {"optimization": {"subPackages": true}, "safearea": {"bottom": {"offset": "none"}}, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "modules": {"Payment": {}, "Barcode": {}, "Bluetooth": {}, "Camera": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a"]}, "ios": {"dSYMs": false}, "sdkConfigs": {"ad": {}, "payment": {"alipay": {"__platform__": ["ios", "android"]}, "weixin": {"__platform__": ["ios", "android"], "appid": "wxdf11ec8626d8a2cb", "UniversalLinks": ""}, "appleiap": {}}}, "icons": {"android": {"hdpi": "static/haotop/image/logo/72.png", "xhdpi": "static/haotop/image/logo/96.png", "xxhdpi": "static/haotop/image/logo/144.png", "xxxhdpi": "static/haotop/image/logo/192.png"}, "ios": {"appstore": "", "ipad": {"app": "", "app@2x": "", "notification": "", "notification@2x": "", "proapp@2x": "", "settings": "", "settings@2x": "", "spotlight": "", "spotlight@2x": ""}, "iphone": {"app@2x": "", "app@3x": "", "notification@2x": "", "notification@3x": "", "settings@2x": "", "settings@3x": "", "spotlight@2x": "", "spotlight@3x": ""}}}, "splashscreen": {"androidStyle": "default", "android": {"hdpi": "static/haotop/image/logo/Start.png", "xhdpi": "static/haotop/image/logo/Start.png", "xxhdpi": "static/haotop/image/logo/Start.png"}}}}, "quickapp": {}, "mp-weixin": {"mergeVirtualHostAttributes": true, "appid": "wx04dd806ca2843cc1", "setting": {"urlCheck": true, "es6": false, "minified": true, "postcss": true}, "optimization": {"subPackages": true}, "usingComponents": true, "permission": {}}, "mp-alipay": {"usingComponents": true, "component2": true}, "mp-qq": {"optimization": {"subPackages": true}, "appid": ""}, "mp-baidu": {"usingComponents": true, "appid": ""}, "mp-toutiao": {"usingComponents": true, "mergeVirtualHostAttributes": true, "appid": ""}, "h5": {"template": "", "router": {"mode": "hash", "base": "hdweb"}, "optimization": {"treeShaking": {"enable": false}}, "title": "浩拓信息技术"}, "permission": {"scope.camera": {"desc": "扫码功能需要使用摄像头权限"}}, "fallbackLocale": "en"}