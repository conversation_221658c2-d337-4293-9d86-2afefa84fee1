{"name": "HAOTOP", "version": "2.3.0", "description": "HAOTOP移动端", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://gitee.com/aidex/aidex-uniapp.git"}, "keywords": ["HAOTOP", "浩拓移动端"], "author": "aidex", "license": "MIT", "bugs": {"url": "https://github.com/aidex/aidex-uniapp/issues"}, "homepage": "https://github.com/aidex/aidex-uniapp#readme", "dependencies": {"@icon-park/vue": "^1.3.5", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "vue-i18n": "^8.20.0"}}