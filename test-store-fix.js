/**
 * 测试 Store 访问修复
 */

const fs = require('fs');

function testStoreFix() {
  console.log('🔍 测试 Store 访问修复...\n');
  
  const checks = [
    {
      name: 'HTTP 拦截器不再使用 vm.$store',
      test: () => {
        try {
          const content = fs.readFileSync('common/http.interceptor.js', 'utf8');
          return !content.includes('vm.$store');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'HTTP 拦截器使用 app.config.globalProperties.$store',
      test: () => {
        try {
          const content = fs.readFileSync('common/http.interceptor.js', 'utf8');
          return content.includes('app.config.globalProperties.$store');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'HTTP 拦截器不再使用 vm.vuex_token',
      test: () => {
        try {
          const content = fs.readFileSync('common/http.interceptor.js', 'utf8');
          return !content.includes('vm.vuex_token');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'HTTP 拦截器使用 store.state.vuex_token',
      test: () => {
        try {
          const content = fs.readFileSync('common/http.interceptor.js', 'utf8');
          return content.includes('store.state.vuex_token');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'HTTP 拦截器不再使用 vm.vuex_remember',
      test: () => {
        try {
          const content = fs.readFileSync('common/http.interceptor.js', 'utf8');
          return !content.includes('vm.vuex_remember');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'HTTP 拦截器使用 store.state.vuex_remember',
      test: () => {
        try {
          const content = fs.readFileSync('common/http.interceptor.js', 'utf8');
          return content.includes('store.state.vuex_remember');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'HTTP 拦截器有 store 可用性检查',
      test: () => {
        try {
          const content = fs.readFileSync('common/http.interceptor.js', 'utf8');
          return content.includes('store ? store.state');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'HTTP 拦截器有默认 baseUrl 备用',
      test: () => {
        try {
          const content = fs.readFileSync('common/http.interceptor.js', 'utf8');
          return content.includes('http://localhost:8980');
        } catch (e) {
          return false;
        }
      }
    }
  ];
  
  let passedChecks = 0;
  const totalChecks = checks.length;
  
  checks.forEach((check, index) => {
    process.stdout.write(`${index + 1}. ${check.name}... `);
    
    try {
      const result = check.test();
      if (result) {
        console.log('✅ 通过');
        passedChecks++;
      } else {
        console.log('❌ 失败');
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
    }
  });
  
  console.log(`\n📊 测试结果: ${passedChecks}/${totalChecks} 通过`);
  
  if (passedChecks === totalChecks) {
    console.log('\n🎉 Store 访问修复成功！');
    console.log('\n✨ 修复内容:');
    console.log('• ✅ 修复了 vm.$store 访问问题');
    console.log('• ✅ 使用 app.config.globalProperties.$store');
    console.log('• ✅ 修复了 vm.vuex_token 访问');
    console.log('• ✅ 修复了 vm.vuex_remember 访问');
    console.log('• ✅ 添加了 store 可用性检查');
    console.log('• ✅ 提供了默认配置备用');
  } else {
    console.log('\n⚠️  部分修复仍有问题。');
  }
  
  console.log('\n🚀 现在应该解决的问题:');
  console.log('• Cannot read property \'$store\' of undefined');
  console.log('• HTTP 拦截器初始化错误');
  console.log('• Token 认证功能');
  console.log('• 记住我功能');
  
  console.log('\n📋 下一步测试:');
  console.log('1. 刷新浏览器页面');
  console.log('2. 检查控制台是否还有 $store 错误');
  console.log('3. 验证页面是否正常显示');
  console.log('4. 测试网络请求功能');
  
  console.log('\n🔧 如果仍有问题:');
  console.log('• 检查 store/index.js 是否正确配置');
  console.log('• 确认 Vuex 4 正确安装');
  console.log('• 验证 main.js 中 store 正确安装');
  
  return passedChecks === totalChecks;
}

// 运行测试
if (require.main === module) {
  const success = testStoreFix();
  process.exit(success ? 0 : 1);
}

module.exports = { testStoreFix };
