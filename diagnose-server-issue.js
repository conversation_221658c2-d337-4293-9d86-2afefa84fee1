/**
 * 诊断服务器和路由问题
 */

const fs = require('fs');

function diagnoseServerIssue() {
  console.log('🔍 诊断服务器和路由问题...\n');
  
  const checks = [
    {
      name: 'vite.config.js 服务器配置检查',
      test: () => {
        try {
          const content = fs.readFileSync('vite.config.js', 'utf8');
          return content.includes('server:') && 
                 content.includes('port: 5100');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'manifest.json H5 配置检查',
      test: () => {
        try {
          const content = fs.readFileSync('manifest.json', 'utf8');
          const manifest = JSON.parse(content);
          return manifest['h5'] !== undefined;
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'pages.json 首页路径检查',
      test: () => {
        try {
          const content = fs.readFileSync('pages.json', 'utf8');
          const config = JSON.parse(content);
          const firstPage = config.pages[0];
          return firstPage && firstPage.path && 
                 fs.existsSync(firstPage.path + '.vue');
        } catch (e) {
          return false;
        }
      }
    },
    {
      name: 'index.html 模板检查',
      test: () => {
        return fs.existsSync('index.html');
      }
    },
    {
      name: 'main.js 入口文件检查',
      test: () => {
        try {
          const content = fs.readFileSync('main.js', 'utf8');
          return content.includes('createSSRApp') &&
                 content.includes('export function createApp');
        } catch (e) {
          return false;
        }
      }
    }
  ];
  
  let passedChecks = 0;
  const totalChecks = checks.length;
  const issues = [];
  
  checks.forEach((check, index) => {
    process.stdout.write(`${index + 1}. ${check.name}... `);
    
    try {
      const result = check.test();
      if (result) {
        console.log('✅ 通过');
        passedChecks++;
      } else {
        console.log('❌ 失败');
        issues.push(check.name);
      }
    } catch (error) {
      console.log('❌ 错误:', error.message);
      issues.push(check.name);
    }
  });
  
  console.log(`\n📊 检查结果: ${passedChecks}/${totalChecks} 通过`);
  
  // 检查 manifest.json 的 H5 配置
  try {
    const manifestContent = fs.readFileSync('manifest.json', 'utf8');
    const manifest = JSON.parse(manifestContent);
    
    console.log('\n📋 H5 配置信息:');
    if (manifest.h5) {
      console.log('• H5 配置存在');
      console.log('• 路由模式:', manifest.h5.router?.mode || '未设置');
      console.log('• 基础路径:', manifest.h5.router?.base || '未设置');
      console.log('• 标题:', manifest.h5.title || manifest.name || '未设置');
    } else {
      console.log('• ❌ 缺少 H5 配置');
    }
  } catch (e) {
    console.log('\n❌ 无法读取 manifest.json H5 配置');
  }
  
  // 检查 pages.json 首页配置
  try {
    const pagesContent = fs.readFileSync('pages.json', 'utf8');
    const pagesConfig = JSON.parse(pagesContent);
    const firstPage = pagesConfig.pages[0];
    
    console.log('\n📋 首页配置信息:');
    console.log('• 首页路径:', firstPage.path);
    console.log('• 首页文件:', firstPage.path + '.vue');
    console.log('• 文件存在:', fs.existsSync(firstPage.path + '.vue') ? '✅' : '❌');
  } catch (e) {
    console.log('\n❌ 无法读取 pages.json 配置');
  }
  
  console.log('\n🔍 观察到的问题:');
  console.log('• 页面 URL 重定向到 /hdweb');
  console.log('• 页面显示空白但有标题');
  console.log('• DOM 结构极简，只有 document 元素');
  
  console.log('\n🔧 可能的原因:');
  console.log('1. **路由配置问题**: H5 路由模式或基础路径配置错误');
  console.log('2. **首页组件问题**: 首页组件无法正确渲染');
  console.log('3. **构建配置问题**: Vite 构建配置不正确');
  console.log('4. **依赖加载问题**: 关键依赖未正确加载');
  
  console.log('\n📋 建议的修复步骤:');
  console.log('1. 检查 manifest.json 中的 H5 配置');
  console.log('2. 确认首页组件能够正确渲染');
  console.log('3. 检查 vite.config.js 的服务器配置');
  console.log('4. 尝试访问具体的页面路径');
  
  console.log('\n🚀 测试建议:');
  console.log('• 尝试访问: http://localhost:5100/hdweb/#/pages/sys/login/index');
  console.log('• 检查 HBuilderX 控制台的编译输出');
  console.log('• 查看浏览器 Network 标签的资源加载情况');
  
  if (issues.length > 0) {
    console.log('\n⚠️  需要修复的问题:');
    issues.forEach(issue => {
      console.log(`• ${issue}`);
    });
  }
  
  return passedChecks === totalChecks;
}

// 运行诊断
if (require.main === module) {
  const success = diagnoseServerIssue();
  process.exit(success ? 0 : 1);
}

module.exports = { diagnoseServerIssue };
